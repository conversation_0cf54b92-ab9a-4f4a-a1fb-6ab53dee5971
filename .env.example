# ===========================================
# Application Settings
# ===========================================

NODE_ENV=development
PORT=3000

# ===========================================
# Frontendapp Application
# ===========================================

FRONTEND_APP_URL=http://localhost:3100/

# ===========================================
# Database Settings
# ===========================================

PGUSER=postgres
PGPASSWORD=my_secure_password
PGHOST=**************
PGPORT=5432
PGDATABASE=hr_master

# Use DATABASE_URL for deployment environments (optional)
# DATABASE_URL=postgresql://postgres:my_secure_password@localhost:5432/hr_master

# ===========================================
# JWT Settings 
# ===========================================

JWT_SECRET=my_jwt_secret
JWT_REFRESH_SECRET=my_jwt_refresh_secret
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# ===========================================
# Email Settings 
# ===========================================

# EMAIL_HOST=smtp.sendgrid.net
# EMAIL_PORT=465
# EMAIL_SECURE=false
# EMAIL_USER=apikey
# EMAIL_PASS=your-sendgrid-api-key-here
# EMAIL_FROM=<EMAIL>

EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key-here
EMAIL_FROM=<EMAIL>

# ===========================================
# Default Admin User Configuration
# ===========================================

# ===========================================
# FIle Storage Service 
# ===========================================

FILE_STORAGE_URL=http://*************:3004
FILE_STORAGE_PROJECT_ID=68651b8f671fe33a39345815


DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=Admin@123


# ===========================================
# Default Admin Credentials 
# ===========================================

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=my_secure_password


# ===========================================
# Default User Credentials 
# ===========================================

DEFAULT_USER_PASSWORD=Temp1234!

# ===========================================
# RATE LIMITING CONFIGURATION
# ===========================================

# Short-term throttling (1 second)
THROTTLE_SHORT_TTL=1000
THROTTLE_SHORT_LIMIT=3

# Medium-term throttling (10 seconds)
THROTTLE_MEDIUM_TTL=10000
THROTTLE_MEDIUM_LIMIT=20

# Long-term throttling (1 minute)
THROTTLE_LONG_TTL=60000
THROTTLE_LONG_LIMIT=100

# Default throttling (1 minute)
THROTTLE_DEFAULT_TTL=60000
THROTTLE_DEFAULT_LIMIT=10DEFAULT_USER_PASSWORD=Temp1234!


# ===========================================
# Google OAuth Configuration
# ===========================================

GOOGLE_CLIENT_ID=95358388666-pgrug4jm4e5l4kv0h6c6lgvv0526lv6r.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=http://localhost:3000/api/auth/google/callback

# ===========================================
# Cron Jobs Configuration
# ===========================================

# Global Settings
CRON_JOBS_ENABLED=true
CRON_TIMEZONE=UTC
CRON_LOGGING_ENABLED=true
CRON_LOGGING_LEVEL=info

# Error Handling
CRON_MAX_RETRIES=3
CRON_RETRY_DELAY=5000

# Individual Job Settings

# Daily Attendance Reminder
CRON_DAILY_ATTENDANCE_REMINDER_ENABLED=true
CRON_DAILY_ATTENDANCE_REMINDER_SCHEDULE="0 9 * * *"
CRON_DAILY_ATTENDANCE_REMINDER_TIMEZONE=UTC

# Auto Clock-Out
CRON_AUTO_CLOCK_OUT_ENABLED=true
CRON_AUTO_CLOCK_OUT_SCHEDULE="0 * * * *"
CRON_AUTO_CLOCK_OUT_MAX_HOURS=12

# Weekly Attendance Report
CRON_WEEKLY_ATTENDANCE_REPORT_ENABLED=true
CRON_WEEKLY_ATTENDANCE_REPORT_SCHEDULE="0 8 * * 1"
CRON_WEEKLY_ATTENDANCE_REPORT_TIMEZONE=UTC

# Monthly Payroll Processing
CRON_MONTHLY_PAYROLL_ENABLED=true
CRON_MONTHLY_PAYROLL_SCHEDULE="0 2 1 * *"
CRON_MONTHLY_PAYROLL_TIMEZONE=UTC

# Database Cleanup
CRON_DATABASE_CLEANUP_ENABLED=true
CRON_DATABASE_CLEANUP_SCHEDULE="0 3 * * 0"
CRON_DATABASE_CLEANUP_RETENTION_DAYS=730

# Health Check
CRON_HEALTH_CHECK_ENABLED=true
CRON_HEALTH_CHECK_SCHEDULE="*/5 * * * *"

# Monitoring Settings
CRON_MONITORING_ENABLED=true
CRON_MONITORING_RETENTION_DAYS=7
CRON_MONITORING_ALERT_THRESHOLD=20

# Lock Settings
CRON_LOCK_ENABLED=true
CRON_LOCK_DEFAULT_DURATION=300000
CRON_LOCK_CLEANUP_INTERVAL=600000

# Performance Settings
CRON_PERFORMANCE_MONITORING=true
CRON_PERFORMANCE_TIMEOUT=300000
CRON_PERFORMANCE_MEMORY_LIMIT=512

# Alert Settings
CRON_ALERT_EMAIL_ENABLED=false
CRON_ALERT_SLACK_ENABLED=false
CRON_ALERT_WEBHOOK_ENABLED=false
CRON_ALERT_WEBHOOK_URL=

# Development Settings
CRON_DEV_MODE=false
CRON_DEV_DRY_RUN=false
CRON_DEV_LOG_LEVEL=debug 