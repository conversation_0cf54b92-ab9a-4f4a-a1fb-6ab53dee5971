import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TimeAttendance } from '../../models/time-attendance.model';
import { TimeAttendanceController } from './controllers/time.attendance.controller';
import { EmployeeTimeAttendanceService } from './services/time-attendance.service';
import { AdminTimeAttendanceController } from './controllers/admin-time-attendance.controller';
import { User } from 'src/models/users-models/user.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
@Module({
  imports: [
    SequelizeModule.forFeature([TimeAttendance, User, EmploymentDetails]),
  ],
  controllers: [TimeAttendanceController, AdminTimeAttendanceController],
  providers: [EmployeeTimeAttendanceService],
  exports: [EmployeeTimeAttendanceService],
})
export class TimeAttendanceModule {}
