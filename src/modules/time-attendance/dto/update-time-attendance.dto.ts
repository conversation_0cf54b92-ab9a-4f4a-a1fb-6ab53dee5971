import { PartialType } from '@nestjs/mapped-types';
import { CreateTimeAttendanceDto } from './create-time-attendance.dto';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsDate,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ATTENDENCE_STATUS } from '../interface/time.attendence.interface';

enum TimeAttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  HALF_DAY = 'half_day',
  EARLY_DEPARTURE = 'early_departure',
}

export class UpdateTimeAttendanceDto extends PartialType(
  CreateTimeAttendanceDto,
) {
  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  date?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  clockInTime?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  clockOutTime?: Date;

  @IsOptional()
  @IsEnum(ATTENDENCE_STATUS)
  status?: ATTENDENCE_STATUS;

  @IsOptional()
  @IsString()
  notes?: string;
}
