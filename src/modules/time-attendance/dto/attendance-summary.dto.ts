export interface AttendanceActivity {
  id: number;
  clockInTime: string | null;
  clockOutTime: string | null;
  notes: string | null;
}

export interface AttendanceDaySummary {
  date: string;
  day: string;
  attendanceActivities: AttendanceActivity[];
  checkIn: string | null;
  checkOut: string | null;
  workedHours: string;
  overtime: string;
  status: string;
  lateBy: string;
  dayType: string;
  isHoliday: boolean;
}

export interface AttendanceSummaryUser {
  id: number;
  name: string;
  profileImage: string | null;
  employeeCode: string | null;
}

export interface AttendanceSummaryResponse {
  success: boolean;
  message: string;
  data: {
    user: AttendanceSummaryUser;
    days: AttendanceDaySummary[];
  };
}
