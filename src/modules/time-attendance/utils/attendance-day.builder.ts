import {
  AttendanceActivity,
  AttendanceDaySummary,
} from '../dto/attendance-summary.dto';
import {
  WORK_START_HOUR,
  WORK_START_MINUTE,
  STANDARD_WORK_HOURS,
  PRESENT_LABEL,
  ABSENT_LABEL,
  WEEKEND_LABEL,
  HOLIDAY_LABEL,
  TIME_FORMAT,
} from 'src/constants/attendance.constants';
import { isWeekend, isHoliday, formatDuration } from 'src/utils/date.utils';

export function buildAttendanceDaySummary(
  date: Date,
  activities: AttendanceActivity[],
  workingDays: string[],
  holidays: string[],
  timeZone: string,
): AttendanceDaySummary {
  const dateStr = date.toLocaleDateString('en-CA', { timeZone }); // YYYY-MM-DD
  const dayOfWeek = date.toLocaleDateString('en-US', {
    weekday: 'short',
    timeZone,
  });
  const weekend = isWeekend(date, workingDays, timeZone);
  const holiday = isHoliday(date, holidays, timeZone);
  let status = ABSENT_LABEL;
  let checkIn: string | null = null;
  let checkOut: string | null = null;
  let workedHours = '00:00';
  let overtime = '00:00';
  let lateBy = '00:00';
  const dayType = holiday ? HOLIDAY_LABEL : weekend ? WEEKEND_LABEL : 'weekday';

  if (holiday) {
    status = ABSENT_LABEL;
  } else if (weekend) {
    status = ABSENT_LABEL;
  } else if (activities.length > 0) {
    status = PRESENT_LABEL;
    // Find earliest clockIn and latest clockOut
    const clockIns = activities
      .filter((a) => a.clockInTime)
      .map((a) => new Date(a.clockInTime!));
    const clockOuts = activities
      .filter((a) => a.clockOutTime)
      .map((a) => new Date(a.clockOutTime!));
    if (clockIns.length > 0) {
      const earliestIn = new Date(
        Math.min(...clockIns.map((d) => d.getTime())),
      );
      checkIn = earliestIn.toLocaleTimeString([], { ...TIME_FORMAT, timeZone });
      // Late by (if clockIn > WORK_START_HOUR:WORK_START_MINUTE)
      if (
        earliestIn.getHours() > WORK_START_HOUR ||
        (earliestIn.getHours() === WORK_START_HOUR &&
          earliestIn.getMinutes() > WORK_START_MINUTE)
      ) {
        const lateMins =
          (earliestIn.getHours() - WORK_START_HOUR) * 60 +
          (earliestIn.getMinutes() - WORK_START_MINUTE);
        lateBy = formatDuration(lateMins);
      }
    }
    if (clockOuts.length > 0) {
      const latestOut = new Date(
        Math.max(...clockOuts.map((d) => d.getTime())),
      );
      checkOut = latestOut.toLocaleTimeString([], { ...TIME_FORMAT, timeZone });
    }
    // Calculate worked hours and overtime
    let totalMs = 0;
    for (const a of activities) {
      if (a.clockInTime && a.clockOutTime) {
        const clockInTime = new Date(a.clockInTime);
        const clockOutTime = new Date(a.clockOutTime);
        totalMs += clockOutTime.getTime() - clockInTime.getTime();
      }
    }
    const totalMins = Math.floor(totalMs / (1000 * 60));
    workedHours = formatDuration(totalMins);
    // Overtime (over STANDARD_WORK_HOURS)
    if (totalMins > STANDARD_WORK_HOURS * 60) {
      const otMins = totalMins - STANDARD_WORK_HOURS * 60;
      overtime = formatDuration(otMins);
    }
  }
  return {
    date: dateStr,
    day: dayOfWeek,
    attendanceActivities: activities,
    checkIn,
    checkOut,
    workedHours,
    overtime,
    status,
    lateBy,
    dayType,
    isHoliday: holiday,
  };
}
