import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { User } from 'src/models/users-models/user.model';
import { Op } from 'sequelize';
import { getDateRange, getDaysInMonth } from 'src/utils/date.util';
import {
  calculateLateAndAbsentDays,
  calculateOvertime,
  calculateWorkedHours,
} from '../utils/time.attendence.utils';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { CompanySettings } from 'src/models/company-settings.model';
import { DEFAULT_WORKING_DAYS } from 'src/constants/attendance.constants';
import { buildAttendanceDaySummary } from '../utils/attendance-day.builder';
import {
  getMondayOfCurrentWeek,
  formatDate,
  clampToToday,
  getNowInTimezone,
} from 'src/utils/date.utils';
import {
  getCompanySettings,
  groupAttendancesByUser,
  buildUserAttendanceSummary,
} from '../utils/time-attendance.helpers';

@Injectable()
export class EmployeeTimeAttendanceService {
  constructor(
    @InjectModel(TimeAttendance)
    private readonly timeAttendanceModel: typeof TimeAttendance,
    private readonly crudService: CrudHelperService,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  async clockIn(user: RequestUserObjectI): Promise<Partial<TimeAttendance>> {
    try {
      const now = new Date();

      const lastAttendance = await this.crudService.findOne<TimeAttendance>(
        TimeAttendance,
        {
          where: { userId: user.id },
          order: [['createdAt', 'DESC']],
        },
      );

      if (lastAttendance && !lastAttendance.clockOutTime) {
        throw new BadRequestException(
          'You have already checked in. Please check out before clocking in again.',
        );
      }

      const attendanceData = {
        userId: user.id,
        clockInTime: now,
        date: now,
      };

      const checkedIn = await this.crudService.create<TimeAttendance>(
        TimeAttendance,
        attendanceData,
      );

      console.log('✅ Stored attendance:', checkedIn);
      return checkedIn;
    } catch (error) {
      console.error('❌ Error during clock-in:', error);
      throw error;
    }
  }

  async clockOut(user: RequestUserObjectI): Promise<TimeAttendance> {
    try {
      const lastClockIn = await this.timeAttendanceModel.findOne({
        where: {
          userId: user.id,
          clockOutTime: null,
        },
        order: [['clockInTime', 'DESC']],
      });

      if (!lastClockIn) {
        throw new NotFoundException(
          'No active clock-in record found to clock out.',
        );
      }

      lastClockIn.clockOutTime = new Date();
      await lastClockIn.save();
      return lastClockIn;
    } catch (error) {
      console.error('❌ Error storing attendance:', error);
      throw error;
    }
  }

  async findPaginated(
    user: RequestUserObjectI,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<TimeAttendance>>> {
    const isPlatformUser = user.role.scope === ROLE_SCOPE_ENUM.PLATFORM;

    const whereClause = isPlatformUser ? {} : { userId: user.id };

    return this.crudService.paginateWithQuery<TimeAttendance>(
      this.timeAttendanceModel,
      {
        page,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        where: whereClause,
      },
    );
  }

  async findByDate(userId: number, date: string): Promise<TimeAttendance[]> {
    const result = await this.timeAttendanceModel.findAll({
      where: { date, userId },
      include: [
        { model: User, attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
      order: [['clockInTime', 'ASC']],
    });
    return result;
  }

  async generateMonthlyReport({
    month,
    year,
  }: {
    month: number;
    year: number;
  }) {
    const { startDate, endDate } = getDateRange(month, year);

    const attendances = await this.timeAttendanceModel.findAll({
      where: {
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    const daysInMonth = getDaysInMonth(month, year);
    const today = new Date();

    const lateAndAbsentStats = calculateLateAndAbsentDays(
      attendances,
      daysInMonth,
      month,
      year,
      today,
    );

    const workedStats = calculateWorkedHours(attendances, daysInMonth);
    const overtimeStats = calculateOvertime(attendances);

    return {
      monthStats: lateAndAbsentStats,
      workedHours: workedStats,
      overTime: overtimeStats,
    };
  }

  private async fetchTimeAttendanceRecords(userIds: number[], date: string) {
    return this.timeAttendanceModel.findAll({
      where: {
        userId: { [Op.in]: userIds },
        date,
      },
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
        },
      ],
      attributes: [
        'id',
        'userId',
        'clockInTime',
        'clockOutTime',
        'date',
        'notes',
        'createdAt',
        'updatedAt',
      ],
    });
  }

  async getCompanyEmployeesTimeAttendance(
    user: RequestUserObjectI,
    page: number = 1,
    limit: number = 10,
  ) {
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await this.userModel.count({
      where: { companyId: user.companyId },
    });

    const companyEmployees = await this.userModel.findAll({
      where: { companyId: user.companyId },
      attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
      include: [
        {
          model: EmploymentDetails,
          required: false,
        },
      ],
      offset,
      limit,
    });

    const companyEmployeesIds = companyEmployees.map((e) => e.id);
    const today = new Date().toISOString().split('T')[0];

    const { workingDays, holidays } = await getCompanySettings(user.companyId);
    const timeAttendances = await this.fetchTimeAttendanceRecords(
      companyEmployeesIds,
      today,
    );
    const attendanceByUser = groupAttendancesByUser(timeAttendances);

    const todayDate = new Date(today);
    const result = companyEmployees.map((employee) => {
      const summary = buildUserAttendanceSummary(
        employee,
        todayDate,
        attendanceByUser[employee.id] || [],
        workingDays,
        holidays,
      );
      return {
        user: {
          id: employee.id,
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          profileImage: employee.profileImage,
          employeeCode: employee.employmentDetails?.[0]?.employeeId || null,
        },
        ...summary,
      };
    });

    // Calculate pagination meta
    const lastPage = Math.ceil(totalCount / limit);

    return {
      success: true,
      message: 'Request successful',
      meta: {
        total: totalCount,
        page: page,
        lastPage: lastPage,
        limit: limit,
        totalRecords: totalCount,
      },
      data: result,
    };
  }

  findAll(): Promise<TimeAttendance[]> {
    return this.crudService.findAll<TimeAttendance>(TimeAttendance, {
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  findByEmployeeId(userId: number): Promise<TimeAttendance[]> {
    return this.crudService.findAll<TimeAttendance>(TimeAttendance, {
      where: { userId },
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  findOne(id: number): Promise<Partial<TimeAttendance>> {
    return this.crudService.findOne<TimeAttendance>(TimeAttendance, {
      where: { id },
      include: { all: true },
    });
  }

  async update(
    id: number,
    dto: UpdateTimeAttendanceDto,
  ): Promise<Partial<TimeAttendance>> {
    await this.findOne(id); // ensure existence
    await this.crudService.update(TimeAttendance, dto, { where: { id } });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.findOne(id);
    await this.crudService.delete(TimeAttendance, { where: { id } });
  }

  async findAllUserIdsForDate(date: Date): Promise<number[]> {
    const records = await this.timeAttendanceModel.findAll({
      where: { date },
      attributes: ['userId'],
    });
    return records.map((r) => r.userId);
  }

  async findAllAttendanceRecordsToAutoClockOut(
    twelveHoursAgo: Date,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: {
        clockInTime: { [Op.lt]: twelveHoursAgo },
        clockOutTime: null,
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName'],
        },
      ],
    });
  }

  async findAllAttendanceRecordsForDateRange(
    start: Date,
    end: Date,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: {
        date: { [Op.between]: [start, end] },
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName'],
        },
      ],
    });
  }

  async deleteAttendanceRecordsOlderThan(cutoff: Date): Promise<number> {
    return this.timeAttendanceModel.destroy({
      where: {
        date: { [Op.lt]: cutoff },
      },
    });
  }

  async getAttendanceSummary(userId: number, start?: string, end?: string) {
    const userInfo = await this.userModel.findByPk(userId, {
      include: [
        {
          model: EmploymentDetails,
          required: false,
        },
      ],
    });

    if (!userInfo) {
      throw new NotFoundException('User not found');
    }

    const timeZone = userInfo.timeZone || 'Asia/Dubai';
    const today = getNowInTimezone(timeZone);
    let endDate = end ? new Date(end) : today;
    endDate = clampToToday(endDate, timeZone);
    const startDate = start ? new Date(start) : getMondayOfCurrentWeek(today);

    const { workingDays, holidays } = await getCompanySettings(
      userInfo.companyId,
    );

    // Fetch attendance records for the entire date range
    const timeAttendances = await this.timeAttendanceModel.findAll({
      where: {
        userId,
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
        },
      ],
      attributes: [
        'id',
        'userId',
        'clockInTime',
        'clockOutTime',
        'date',
        'notes',
        'createdAt',
        'updatedAt',
      ],
    });

    const attendanceByDate = timeAttendances.reduce(
      (acc, record) => {
        const dateStr = new Date(record.date).toISOString().split('T')[0];
        if (!acc[dateStr]) {
          acc[dateStr] = [];
        }
        acc[dateStr].push({
          id: record.id,
          clockInTime: record.clockInTime
            ? record.clockInTime.toISOString()
            : null,
          clockOutTime: record.clockOutTime
            ? record.clockOutTime.toISOString()
            : null,
          notes: record.notes || null,
        });
        return acc;
      },
      {} as Record<string, any[]>,
    );

    const days = [];
    for (
      let d = new Date(startDate);
      d <= endDate;
      d.setDate(d.getDate() + 1)
    ) {
      const dateStr = d.toISOString().split('T')[0];
      const summary = buildUserAttendanceSummary(
        userInfo,
        d,
        attendanceByDate[dateStr] || [],
        workingDays,
        holidays,
      );
      days.push(summary);
    }

    return {
      success: true,
      message: 'Request successful',
      data: {
        user: {
          id: userInfo.id,
          name: `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim(),
          profileImage: userInfo.profileImage,
          employeeCode: userInfo.employmentDetails?.[0]?.employeeId || null,
        },
        days,
      },
    };
  }
}
