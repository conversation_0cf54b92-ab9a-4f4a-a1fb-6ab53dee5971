import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { User } from 'src/models/users-models/user.model';
import { Op } from 'sequelize';
import { getDateRange, getDaysInMonth } from 'src/utils/date.util';
import {
  calculateLateAndAbsentDays,
  calculateOvertime,
  calculateWorkedHours,
} from '../utils/time.attendence.utils';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { CompanySettings } from 'src/models/company-settings.model';
import { DEFAULT_WORKING_DAYS } from 'src/constants/attendance.constants';
import { buildAttendanceDaySummary } from '../utils/attendance-day.builder';
import {
  getMondayOfCurrentWeek,
  formatDate,
  clampToToday,
  getNowInTimezone,
} from 'src/utils/date.utils';

@Injectable()
export class EmployeeTimeAttendanceService {
  constructor(
    @InjectModel(TimeAttendance)
    private readonly timeAttendanceModel: typeof TimeAttendance,
    private readonly crudService: CrudHelperService,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  async clockIn(user: RequestUserObjectI): Promise<Partial<TimeAttendance>> {
    try {
      const now = new Date();

      const lastAttendance = await this.crudService.findOne<TimeAttendance>(
        TimeAttendance,
        {
          where: { userId: user.id },
          order: [['createdAt', 'DESC']],
        },
      );

      if (lastAttendance && !lastAttendance.clockOutTime) {
        throw new BadRequestException(
          'You have already checked in. Please check out before clocking in again.',
        );
      }

      const attendanceData = {
        userId: user.id,
        clockInTime: now,
        date: now,
      };

      const checkedIn = await this.crudService.create<TimeAttendance>(
        TimeAttendance,
        attendanceData,
      );

      console.log('✅ Stored attendance:', checkedIn);
      return checkedIn;
    } catch (error) {
      console.error('❌ Error during clock-in:', error);
      throw error;
    }
  }

  async clockOut(user: RequestUserObjectI): Promise<TimeAttendance> {
    try {
      const lastClockIn = await this.timeAttendanceModel.findOne({
        where: {
          userId: user.id,
          clockOutTime: null,
        },
        order: [['clockInTime', 'DESC']],
      });

      if (!lastClockIn) {
        throw new NotFoundException(
          'No active clock-in record found to clock out.',
        );
      }

      lastClockIn.clockOutTime = new Date();
      await lastClockIn.save();
      return lastClockIn;
    } catch (error) {
      console.error('❌ Error storing attendance:', error);
      throw error;
    }
  }

  async findPaginated(
    user: RequestUserObjectI,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<TimeAttendance>>> {
    const isPlatformUser = user.role.scope === ROLE_SCOPE_ENUM.PLATFORM;

    const whereClause = isPlatformUser ? {} : { userId: user.id };

    return this.crudService.paginateWithQuery<TimeAttendance>(
      this.timeAttendanceModel,
      {
        page,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        where: whereClause,
      },
    );
  }

  async findByDate(date: string): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: { date },
      include: [
        { model: User, attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
      order: [['clockInTime', 'ASC']],
    });
  }

  async generateMonthlyReport({
    month,
    year,
  }: {
    month: number;
    year: number;
  }) {
    const { startDate, endDate } = getDateRange(month, year);

    const attendances = await this.timeAttendanceModel.findAll({
      where: {
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    const daysInMonth = getDaysInMonth(month, year);
    const today = new Date();

    const lateAndAbsentStats = calculateLateAndAbsentDays(
      attendances,
      daysInMonth,
      month,
      year,
      today,
    );

    const workedStats = calculateWorkedHours(attendances, daysInMonth);
    const overtimeStats = calculateOvertime(attendances);

    return {
      monthStats: lateAndAbsentStats,
      workedHours: workedStats,
      overTime: overtimeStats,
    };
  }

  findAll(): Promise<TimeAttendance[]> {
    return this.crudService.findAll<TimeAttendance>(TimeAttendance, {
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  findByEmployeeId(userId: number): Promise<TimeAttendance[]> {
    return this.crudService.findAll<TimeAttendance>(TimeAttendance, {
      where: { userId },
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  findOne(id: number): Promise<Partial<TimeAttendance>> {
    return this.crudService.findOne<TimeAttendance>(TimeAttendance, {
      where: { id },
      include: { all: true },
    });
  }

  async update(
    id: number,
    dto: UpdateTimeAttendanceDto,
  ): Promise<Partial<TimeAttendance>> {
    await this.findOne(id); // ensure existence
    await this.crudService.update(TimeAttendance, dto, { where: { id } });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.findOne(id);
    await this.crudService.delete(TimeAttendance, { where: { id } });
  }

  async findAllUserIdsForDate(date: Date): Promise<number[]> {
    const records = await this.timeAttendanceModel.findAll({
      where: { date },
      attributes: ['userId'],
    });
    return records.map((r) => r.userId);
  }

  async findAllAttendanceRecordsToAutoClockOut(
    twelveHoursAgo: Date,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: {
        clockInTime: { [Op.lt]: twelveHoursAgo },
        clockOutTime: null,
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName'],
        },
      ],
    });
  }

  async findAllAttendanceRecordsForDateRange(
    start: Date,
    end: Date,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: {
        date: { [Op.between]: [start, end] },
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName'],
        },
      ],
    });
  }

  async deleteAttendanceRecordsOlderThan(cutoff: Date): Promise<number> {
    return this.timeAttendanceModel.destroy({
      where: {
        date: { [Op.lt]: cutoff },
      },
    });
  }

  async getAttendanceSummary(userId: number, start?: string, end?: string) {
    // Fetch user info and get timezone
    const userInfo = await this.userModel.findByPk(userId, {
      include: [
        {
          model: EmploymentDetails,
          required: false,
        },
      ],
    });
    const employment = userInfo?.employmentDetails?.[0];
    const timeZone = userInfo?.timeZone || 'Asia/Dubai';

    // Determine today, startDate, endDate in user's timezone
    const today = getNowInTimezone(timeZone);
    let endDate = end ? new Date(end) : today;
    endDate = clampToToday(endDate, timeZone);
    let startDate: Date;
    if (start) {
      startDate = new Date(start);
    } else {
      startDate = getMondayOfCurrentWeek(today);
    }
    const startStr = formatDate(startDate, timeZone);
    const endStr = formatDate(endDate, timeZone);

    // Fetch company settings for weekends/holidays
    let companySettings: CompanySettings | null = null;
    if (userInfo?.companyId) {
      companySettings = await CompanySettings.findOne({
        where: { companyId: userInfo.companyId },
      });
    }
    const workingDays = companySettings?.workingDays || DEFAULT_WORKING_DAYS;
    const holidays = (companySettings?.holidayDays || []).map((d) =>
      new Date(d).toLocaleDateString('en-US', { timeZone }),
    );

    // Fetch all attendance records for the user in the range
    const attendances = await this.timeAttendanceModel.findAll({
      where: {
        userId: userId,
        date: { [Op.between]: [startStr, endStr] },
      },
      order: [['clockInTime', 'ASC']],
    });
    // Group attendances by date
    const attendanceMap = new Map<string, any[]>();
    for (const att of attendances) {
      const dateStr = new Date(att.date).toLocaleDateString('en-US', {
        timeZone,
      });
      if (!attendanceMap.has(dateStr)) attendanceMap.set(dateStr, []);
      attendanceMap.get(dateStr)!.push({
        id: att.id,
        clockInTime: att.clockInTime ? att.clockInTime.toISOString() : null,
        clockOutTime: att.clockOutTime ? att.clockOutTime.toISOString() : null,
        notes: att.notes || null,
      });
    }

    // Build days array using builder and constants
    const days = [];
    for (
      let d = new Date(startDate);
      d <= endDate;
      d.setDate(d.getDate() + 1)
    ) {
      const dateKey = d.toLocaleDateString('en-US', { timeZone });
      const activities = attendanceMap.get(dateKey) || [];
      days.push(
        buildAttendanceDaySummary(
          new Date(d),
          activities,
          workingDays,
          holidays,
          timeZone,
        ),
      );
    }

    return {
      success: true,
      message: 'Request successful',
      data: {
        user: {
          id: userInfo?.id,
          name: `${userInfo?.firstName || ''} ${userInfo?.lastName || ''}`.trim(),
          profileImage: userInfo?.profileImage,
          employeeCode: employment?.employeeId || null,
        },
        days,
      },
    };
  }
}
