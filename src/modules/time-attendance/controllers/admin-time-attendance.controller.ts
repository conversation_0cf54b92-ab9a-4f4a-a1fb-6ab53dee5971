import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';
import { CreateTimeAttendanceDto } from '../dto/create-time-attendance.dto';

@Controller('admin/time-attendance')
@UseGuards(JwtAuthGuard)
export class AdminTimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Post('clock-in')
  async clockIn(@Req() req: UserRequestI): Promise<Partial<TimeAttendance>> {
    const user = req.user;
    return this.timeAttendanceService.clockIn(user);
  }

  @Patch('clock-out')
  async clockOut(@Req() req: UserRequestI): Promise<TimeAttendance> {
    const user = req.user;
    return this.timeAttendanceService.clockOut(user);
  }

  @Get('paginated')
  async findPaginated(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Req() req: UserRequestI,
  ): Promise<PaginatedResult<Partial<TimeAttendance>>> {
    const user = req.user;
    return this.timeAttendanceService.findPaginated(
      user,
      Number(page),
      Number(limit),
    );
  }

  @Get()
  async findAll(): Promise<TimeAttendance[]> {
    return this.timeAttendanceService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceService.findByEmployeeId(employeeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateTimeAttendanceDto: UpdateTimeAttendanceDto,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.update(id, updateTimeAttendanceDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.timeAttendanceService.remove(id);
  }
}
