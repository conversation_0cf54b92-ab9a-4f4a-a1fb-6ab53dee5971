import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';
import { CanView } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
@Controller('time-attendance')
@UseGuards(JwtAuthGuard)
export class TimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Post('clock-in')
  async clockIn(@Req() req: UserRequestI): Promise<Partial<TimeAttendance>> {
    const user = req.user;
    return this.timeAttendanceService.clockIn(user);
  }

  @Patch('clock-out')
  async clockOut(@Req() req: UserRequestI): Promise<TimeAttendance> {
    const user = req.user;
    return this.timeAttendanceService.clockOut(user);
  }

  @Get('paginated')
  async findPaginated(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Req() req: UserRequestI,
  ): Promise<PaginatedResult<Partial<TimeAttendance>>> {
    const user = req.user;
    return this.timeAttendanceService.findPaginated(
      user,
      Number(page),
      Number(limit),
    );
  }

  @Get('company-employees')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getCompanyEmployeesTimeAttendance(
    @Req() req: UserRequestI,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    return this.timeAttendanceService.getCompanyEmployeesTimeAttendance(
      req.user,
      parseInt(page),
      parseInt(limit),
    );
  }

  @Get('by-date')
  async findByDate(
    @Req() req: UserRequestI,
    @Query('date') date: string,
  ): Promise<TimeAttendance[]> {
    if (!date)
      throw new BadRequestException('Date query parameter is required');
    return this.timeAttendanceService.findByDate(req.user.id, date);
  }

  @Get('report')
  async getMonthlyReport(
    @Query('month') month: number,
    @Query('year') year: number,
  ) {
    return this.timeAttendanceService.generateMonthlyReport({ month, year });
  }

  @Get('summary')
  async getAttendanceSummary(
    @Query('start') start: string,
    @Query('end') end: string,
    @Req() req: UserRequestI,
  ) {
    return this.timeAttendanceService.getAttendanceSummary(
      req.user.id,
      start,
      end,
    );
  }

  @Get()
  async findAll(): Promise<TimeAttendance[]> {
    return this.timeAttendanceService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceService.findByEmployeeId(employeeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateTimeAttendanceDto: UpdateTimeAttendanceDto,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.update(id, updateTimeAttendanceDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.timeAttendanceService.remove(id);
  }
}
