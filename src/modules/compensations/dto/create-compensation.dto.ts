import {
  IsDateString,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsJ<PERSON><PERSON>,
} from 'class-validator';

export class CreateCompensationDto {
  @IsNumber()
  candidateId: number;

  @IsDateString()
  effectiveFrom: Date;

  @IsDateString()
  @IsOptional()
  reviewDate?: Date;

  @IsString()
  currency: string;

  @IsString()
  frequency: string;

  @IsNumber()
  totalSalary: number;

  @IsNumber()
  basicSalary: number;

  @IsNumber()
  @IsOptional()
  housing?: number;

  @IsNumber()
  @IsOptional()
  transportation?: number;
}
