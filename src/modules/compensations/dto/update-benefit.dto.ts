import { IsString, <PERSON>N<PERSON>ber, IsInt, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateBenefitDto {
  @IsString()
  @IsOptional()
  allowance?: string;

  @IsString()
  @IsOptional()
  frequency?: string;

  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  amount?: number;

  @IsInt()
  @Type(() => Number)
  @IsOptional()
  compensationId?: number;
}
