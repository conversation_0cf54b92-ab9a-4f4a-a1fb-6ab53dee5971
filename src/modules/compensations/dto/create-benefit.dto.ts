import { IsString, IsN<PERSON>ber, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateBenefitDto {
  @IsString()
  allowance: string; // e.g., "Flight Ticket", "Health Insurance", "Car Allowance"

  @IsString()
  frequency: string; // e.g., "Monthly", "Annually", "One-time"

  @IsNumber()
  @Type(() => Number)
  amount: number;

  @IsInt()
  @Type(() => Number)
  compensationId: number;
}
