import {
  IsDateString,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>J<PERSON><PERSON>,
} from 'class-validator';

export class UpdateCompensationDto {
  @IsNumber()
  @IsOptional()
  candidateId?: number;

  @IsDateString()
  @IsOptional()
  effectiveFrom?: Date;

  @IsDateString()
  @IsOptional()
  reviewDate?: Date;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  frequency?: string;

  @IsNumber()
  @IsOptional()
  totalSalary?: number;

  @IsNumber()
  @IsOptional()
  basicSalary?: number;

  @IsNumber()
  @IsOptional()
  housing?: number;

  @IsNumber()
  @IsOptional()
  transportation?: number;
}
