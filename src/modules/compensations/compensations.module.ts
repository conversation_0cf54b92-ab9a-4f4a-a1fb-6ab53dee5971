import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Compensation } from '../../models/users-models/compensation.model';
import { Benefit } from '../../models/users-models/benefits.model';
import { Candidate } from '../../models/users-models/candidate.model';
import { CompensationsService } from './services/compensations.service';
import { BenefitsService } from './services/benefits.service';
import { CompensationsController } from './controllers/compensations.controller';
import { BenefitsController } from './controllers/benefits.controller';

@Module({
  imports: [SequelizeModule.forFeature([Compensation, Benefit, Candidate])],
  providers: [CompensationsService, BenefitsService],
  controllers: [CompensationsController, BenefitsController],
  exports: [CompensationsService, BenefitsService],
})
export class CompensationsModule {}
