import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Compensation } from '../../../models/users-models/compensation.model';
import { Benefit } from '../../../models/users-models/benefits.model';
import { Candidate } from '../../../models/users-models/candidate.model';
import { CreateCompensationDto } from '../dto/create-compensation.dto';
import { UpdateCompensationDto } from '../dto/update-compensation.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { TenantAwareBaseService } from '../../../common/services/tenant-aware-base.service';
import { TenantContextI } from '../../../common/types/tenant-context.types';

@Injectable()
export class CompensationsService extends TenantAwareBaseService {
  constructor(
    @InjectModel(Compensation)
    private compensationModel: typeof Compensation,
    crudHelperService: CrudHelperService,
    @InjectModel(Candidate)
    private candidateModel: typeof Candidate,
  ) {
    super(crudHelperService);
  }

  async create(dto: CreateCompensationDto): Promise<Compensation> {
    return this.compensationModel.create({ ...dto });
  }

  async findOne(
    id: number,
    tenantContext: TenantContextI,
  ): Promise<Compensation> {
    const record = await this.compensationModel.findOne({
      where: { id },
      include: [
        {
          model: Candidate,
          where: { companyId: tenantContext.companyId },
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Benefit,
        },
      ],
    });
    if (!record) throw new NotFoundException('Not found or not authorized');
    return record;
  }

  async update(
    id: number,
    dto: UpdateCompensationDto,
    tenantContext: TenantContextI,
  ): Promise<Compensation> {
    const record = await this.validateTenantAccess(tenantContext, id, {
      model: Compensation,
    });
    if (!record) throw new NotFoundException('Not authorized');
    await record.update(dto);
    return record;
  }

  async remove(id: number, tenantContext: TenantContextI): Promise<void> {
    const record = await this.validateTenantAccess(tenantContext, id, {
      model: Compensation,
    });
    if (!record) throw new NotFoundException('Not authorized');
    await record.destroy();
  }

  async findByCandidateId(
    candidateId: number,
    tenantContext: TenantContextI,
  ): Promise<Compensation> {
    // Check if candidate belongs to the same company as tenantContext
    const candidate = await this.candidateModel.findOne({
      where: {
        id: candidateId,
        companyId: tenantContext.companyId,
      },
    });
    if (!candidate) {
      throw new NotFoundException('Candidate does not belong to your company');
    }
    const compensation = await this.compensationModel.findOne({
      where: { candidateId },
      include: [
        {
          model: Benefit,
        },
        {
          model: Candidate,
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });
    if (!compensation) {
      throw new NotFoundException('Compensation not found');
    }
    return compensation;
  }

  async findAll(tenantContext: TenantContextI): Promise<Compensation[]> {
    return this.compensationModel.findAll({
      include: [
        {
          model: Candidate,
          where: { companyId: tenantContext.companyId },
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Benefit,
        },
      ],
    });
  }
}
