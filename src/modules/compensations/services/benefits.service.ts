import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Benefit } from '../../../models/users-models/benefits.model';
import { Compensation } from '../../../models/users-models/compensation.model';
import { Candidate } from '../../../models/users-models/candidate.model';
import { CreateBenefitDto } from '../dto/create-benefit.dto';
import { UpdateBenefitDto } from '../dto/update-benefit.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';

@Injectable()
export class BenefitsService {
  constructor(
    @InjectModel(Benefit)
    private benefitModel: typeof Benefit,
    @InjectModel(Compensation)
    private compensationModel: typeof Compensation,
    private crudHelperService: CrudHelperService,
  ) {}

  async create(dto: CreateBenefitDto): Promise<any> {
    const compensation = await this.crudHelperService.findOne(
      this.compensationModel,
      {
        where: { id: dto.compensationId },
        include: [
          {
            model: Candidate,
          },
        ],
      },
    );
    if (!compensation) {
      throw new NotFoundException('Compensation not found');
    }
    return this.crudHelperService.create(this.benefitModel, { ...dto });
  }

  async findAll(): Promise<any[]> {
    return this.crudHelperService.findAll(this.benefitModel, {
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  }

  async findByCompensationId(compensationId: number): Promise<any[]> {
    // Verify that the compensation exists
    const compensation = await this.crudHelperService.findOne(
      this.compensationModel,
      {
        where: { id: compensationId },
        include: [
          {
            model: Candidate,
          },
        ],
      },
    );
    if (!compensation) {
      throw new NotFoundException('Compensation not found');
    }
    return this.crudHelperService.findAll(this.benefitModel, {
      where: { compensationId },
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  }

  async findOne(id: number): Promise<any> {
    const benefit = await this.crudHelperService.findOne(this.benefitModel, {
      where: { id },
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
    if (!benefit) {
      throw new NotFoundException('Benefit not found');
    }
    return benefit;
  }

  async update(id: number, dto: UpdateBenefitDto): Promise<any> {
    const benefit = await this.findOne(id);
    // If compensationId is being updated, verify the new compensation
    if (dto.compensationId && dto.compensationId !== benefit.compensationId) {
      const newCompensation = await this.crudHelperService.findOne(
        this.compensationModel,
        {
          where: { id: dto.compensationId },
          include: [
            {
              model: Candidate,
            },
          ],
        },
      );
      if (!newCompensation) {
        throw new NotFoundException('New compensation not found');
      }
    }
    await this.crudHelperService.update(this.benefitModel, dto, {
      where: { id },
    });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.findOne(id); // Ensure benefit exists
    await this.crudHelperService.delete(this.benefitModel, { where: { id } });
  }

  async findByCandidateId(candidateId: number): Promise<any[]> {
    // First find the candidate's compensation
    const compensation = await this.crudHelperService.findOne(
      this.compensationModel,
      {
        where: { candidateId },
        include: [
          {
            model: Candidate,
          },
        ],
      },
    );
    if (!compensation || !(compensation as any).id) {
      throw new NotFoundException('Candidate compensation not found');
    }
    return this.crudHelperService.findAll(this.benefitModel, {
      where: { compensationId: (compensation as any).id },
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  }
}
