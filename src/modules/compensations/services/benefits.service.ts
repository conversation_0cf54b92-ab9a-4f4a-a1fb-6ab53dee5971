import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Benefit } from '../../../models/users-models/benefits.model';
import { Compensation } from '../../../models/users-models/compensation.model';
import { Candidate } from '../../../models/users-models/candidate.model';
import { CreateBenefitDto } from '../dto/create-benefit.dto';
import { UpdateBenefitDto } from '../dto/update-benefit.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { TenantAwareBaseService } from '../../../common/services/tenant-aware-base.service';
import { TenantContextI } from '../../../common/types/tenant-context.types';

@Injectable()
export class BenefitsService extends TenantAwareBaseService {
  constructor(
    @InjectModel(Benefit)
    private benefitModel: typeof Benefit,
    @InjectModel(Compensation)
    private compensationModel: typeof Compensation,
    @InjectModel(Candidate)
    private candidateModel: typeof Candidate,
    crudHelperService: CrudHelperService,
  ) {
    super(crudHelperService);
  }

  async create(
    dto: CreateBenefitDto,
    tenantContext: TenantContextI,
  ): Promise<Benefit> {
    // Verify that the compensation belongs to the candidate's company
    const compensation = await this.compensationModel.findOne({
      where: { id: dto.compensationId },
      include: [
        {
          model: Candidate,
          where: { companyId: tenantContext.companyId },
        },
      ],
    });

    if (!compensation) {
      throw new NotFoundException('Compensation not found or not authorized');
    }

    return this.benefitModel.create({ ...dto });
  }

  async findAll(tenantContext: TenantContextI): Promise<Benefit[]> {
    return this.benefitModel.findAll({
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              where: { companyId: tenantContext.companyId },
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  }

  async findByCompensationId(
    compensationId: number,
    tenantContext: TenantContextI,
  ): Promise<Benefit[]> {
    // Verify that the compensation belongs to the candidate's company
    const compensation = await this.compensationModel.findOne({
      where: { id: compensationId },
      include: [
        {
          model: Candidate,
          where: { companyId: tenantContext.companyId },
        },
      ],
    });

    if (!compensation) {
      throw new NotFoundException('Compensation not found or not authorized');
    }

    return this.benefitModel.findAll({
      where: { compensationId },
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  }

  async findOne(id: number, tenantContext: TenantContextI): Promise<Benefit> {
    const benefit = await this.benefitModel.findOne({
      where: { id },
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              where: { companyId: tenantContext.companyId },
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    if (!benefit) {
      throw new NotFoundException('Benefit not found or not authorized');
    }

    return benefit;
  }

  async update(
    id: number,
    dto: UpdateBenefitDto,
    tenantContext: TenantContextI,
  ): Promise<Benefit> {
    const benefit = await this.findOne(id, tenantContext);

    // If compensationId is being updated, verify the new compensation
    if (dto.compensationId && dto.compensationId !== benefit.compensationId) {
      const newCompensation = await this.compensationModel.findOne({
        where: { id: dto.compensationId },
        include: [
          {
            model: Candidate,
            where: { companyId: tenantContext.companyId },
          },
        ],
      });

      if (!newCompensation) {
        throw new NotFoundException(
          'New compensation not found or not authorized',
        );
      }
    }

    await benefit.update(dto);
    return benefit;
  }

  async remove(id: number, tenantContext: TenantContextI): Promise<void> {
    const benefit = await this.findOne(id, tenantContext);
    await benefit.destroy();
  }

  async findByCandidateId(
    candidateId: number,
    tenantContext: TenantContextI,
  ): Promise<Benefit[]> {
    // First find the candidate's compensation
    const compensation = await this.compensationModel.findOne({
      where: { candidateId },
      include: [
        {
          model: Candidate,
          where: { companyId: tenantContext.companyId },
        },
      ],
    });

    if (!compensation) {
      throw new NotFoundException(
        'Candidate compensation not found or not authorized',
      );
    }

    return this.benefitModel.findAll({
      where: { compensationId: compensation.id },
      include: [
        {
          model: Compensation,
          include: [
            {
              model: Candidate,
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  }
}
