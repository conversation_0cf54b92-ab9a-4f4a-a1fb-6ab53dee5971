import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Put,
  Req,
} from '@nestjs/common';
import { CompensationsService } from '../services/compensations.service';
import { CreateCompensationDto } from '../dto/create-compensation.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { UpdateCompensationDto } from '../dto/update-compensation.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';

@Controller('compensations')
@UseGuards(PermissionGuard)
export class CompensationsController {
  constructor(private readonly compensationsService: CompensationsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  async create(@Body() dto: CreateCompensationDto, @Req() req: UserRequestI) {
    const result = await this.compensationsService.create(
      dto,
      req.user.companyId,
    );
    return {
      success: true,
      message: 'Compensation and benefits created successfully',
      data: result,
    };
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  async findAll(@Req() req: UserRequestI) {
    const result = await this.compensationsService.findAll(req.user.companyId);
    return {
      success: true,
      message: 'Compensations retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    const result = await this.compensationsService.findOne(
      id,
      req.user.companyId,
    );
    return {
      success: true,
      message: 'Compensation retrieved successfully',
      data: result,
    };
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateCompensationDto,
    @Req() req: UserRequestI,
  ) {
    const result = await this.compensationsService.update(
      id,
      dto,
      req.user.companyId,
    );
    return {
      success: true,
      message: 'Compensation updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    await this.compensationsService.remove(id, req.user.companyId);
    return {
      success: true,
      message: 'Compensation deleted successfully',
    };
  }

  @Get('candidate/:candidateId')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findByCandidateId(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @Req() req: UserRequestI,
  ) {
    const result = await this.compensationsService.findByCandidateId(
      candidateId,
      req.user.companyId,
    );
    return {
      success: true,
      message: 'Candidate compensation retrieved successfully',
      data: result,
    };
  }
}
