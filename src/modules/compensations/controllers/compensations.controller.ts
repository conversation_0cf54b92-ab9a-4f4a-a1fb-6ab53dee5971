import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Put,
} from '@nestjs/common';
import { CompensationsService } from '../services/compensations.service';
import { CreateCompensationDto } from '../dto/create-compensation.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { UpdateCompensationDto } from '../dto/update-compensation.dto';
import { TenantContextI } from 'src/common/types/tenant-context.types';
import { TenantContext } from 'src/common/decorators/tenant-context.decorator';

@Controller('compensations')
@UseGuards(PermissionGuard)
export class CompensationsController {
  constructor(private readonly compensationsService: CompensationsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  async create(
    @Body() dto: CreateCompensationDto,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.compensationsService.create(dto, tenantContext);
    return {
      success: true,
      message: 'Compensation and benefits created successfully',
      data: result,
    };
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  async findAll(@TenantContext() tenantContext: TenantContextI) {
    const result = await this.compensationsService.findAll(tenantContext);
    return {
      success: true,
      message: 'Compensations retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.compensationsService.findOne(id, tenantContext);
    return {
      success: true,
      message: 'Compensation retrieved successfully',
      data: result,
    };
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateCompensationDto,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.compensationsService.update(
      id,
      dto,
      tenantContext,
    );
    return {
      success: true,
      message: 'Compensation updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    await this.compensationsService.remove(id, tenantContext);
    return {
      success: true,
      message: 'Compensation deleted successfully',
    };
  }

  @Get('candidate/:candidateId')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findByCandidateId(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.compensationsService.findByCandidateId(
      candidateId,
      tenantContext,
    );
    return {
      success: true,
      message: 'Candidate compensation retrieved successfully',
      data: result,
    };
  }
}
