import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Req,
} from '@nestjs/common';
import { BenefitsService } from '../services/benefits.service';
import { CreateBenefitDto } from '../dto/create-benefit.dto';
import { UpdateBenefitDto } from '../dto/update-benefit.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
  AuthenticatedOnly,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';
import { UserRequestI } from '../../auth/auth.interfaces';

@Controller('benefits')
@UseGuards(PermissionGuard)
export class BenefitsController {
  constructor(private readonly benefitsService: BenefitsService) {}

  @Post()
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async create(@Body() createBenefitDto: CreateBenefitDto) {
    const result = await this.benefitsService.create(createBenefitDto);
    return {
      success: true,
      message: 'Benefit created successfully',
      data: result,
    };
  }

  @Get()
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async findAll() {
    const result = await this.benefitsService.findAll();
    return {
      success: true,
      message: 'Benefits retrieved successfully',
      data: result,
    };
  }

  @Get('compensation/:compensationId')
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async findByCompensationId(
    @Param('compensationId', ParseIntPipe) compensationId: number,
  ) {
    const result =
      await this.benefitsService.findByCompensationId(compensationId);
    return {
      success: true,
      message: 'Benefits retrieved successfully',
      data: result,
    };
  }

  @Get('candidate/:candidateId')
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async findByCandidateId(
    @Param('candidateId', ParseIntPipe) candidateId: number,
  ) {
    const result = await this.benefitsService.findByCandidateId(candidateId);
    return {
      success: true,
      message: 'Candidate benefits retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const result = await this.benefitsService.findOne(id);
    return {
      success: true,
      message: 'Benefit retrieved successfully',
      data: result,
    };
  }

  @Patch(':id')
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateBenefitDto: UpdateBenefitDto,
  ) {
    const result = await this.benefitsService.update(id, updateBenefitDto);
    return {
      success: true,
      message: 'Benefit updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @AuthenticatedOnly()
  @ThrottleRelaxed()
  async remove(@Param('id', ParseIntPipe) id: number) {
    await this.benefitsService.remove(id);
    return {
      success: true,
      message: 'Benefit deleted successfully',
    };
  }
}
