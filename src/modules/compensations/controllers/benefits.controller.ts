import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { BenefitsService } from '../services/benefits.service';
import { CreateBenefitDto } from '../dto/create-benefit.dto';
import { UpdateBenefitDto } from '../dto/update-benefit.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';
import { TenantContext } from '../../../common/decorators/tenant-context.decorator';
import { TenantContextI } from '../../../common/types/tenant-context.types';

@Controller('benefits')
@UseGuards(PermissionGuard)
export class BenefitsController {
  constructor(private readonly benefitsService: BenefitsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async create(
    @Body() createBenefitDto: CreateBenefitDto,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.benefitsService.create(
      createBenefitDto,
      tenantContext,
    );
    return {
      success: true,
      message: 'Benefit created successfully',
      data: result,
    };
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findAll(@TenantContext() tenantContext: TenantContextI) {
    const result = await this.benefitsService.findAll(tenantContext);
    return {
      success: true,
      message: 'Benefits retrieved successfully',
      data: result,
    };
  }

  @Get('compensation/:compensationId')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findByCompensationId(
    @Param('compensationId', ParseIntPipe) compensationId: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.benefitsService.findByCompensationId(
      compensationId,
      tenantContext,
    );
    return {
      success: true,
      message: 'Benefits retrieved successfully',
      data: result,
    };
  }

  @Get('candidate/:candidateId')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findByCandidateId(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.benefitsService.findByCandidateId(
      candidateId,
      tenantContext,
    );
    return {
      success: true,
      message: 'Candidate benefits retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.benefitsService.findOne(id, tenantContext);
    return {
      success: true,
      message: 'Benefit retrieved successfully',
      data: result,
    };
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateBenefitDto: UpdateBenefitDto,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    const result = await this.benefitsService.update(
      id,
      updateBenefitDto,
      tenantContext,
    );
    return {
      success: true,
      message: 'Benefit updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    await this.benefitsService.remove(id, tenantContext);
    return {
      success: true,
      message: 'Benefit deleted successfully',
    };
  }
}
