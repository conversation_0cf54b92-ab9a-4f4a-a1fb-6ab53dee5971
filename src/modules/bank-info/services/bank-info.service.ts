import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { BankInfo } from '../../../models/users-models/bank-info.model';
import { CreateBankInfoDto } from '../dto/create-bank-info.dto';
import { UpdateBankInfoDto } from '../dto/update-bank-info.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { User } from 'src/models/users-models/user.model';

@Injectable()
export class BankInfoService {
  constructor(
    @InjectModel(BankInfo)
    private bankInfoModel: typeof BankInfo,
    private crudHelperService: CrudHelperService,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async create(
    dto: CreateBankInfoDto,
    companyId: number,
  ): Promise<Partial<BankInfo>> {
    return this.crudHelperService.create(this.bankInfoModel, {
      ...dto,
      companyId,
    }) as Promise<Partial<BankInfo>>;
  }

  async findOne(id: number, companyId: number): Promise<BankInfo> {
    const bankInfo = await this.crudHelperService.findOne(this.bankInfoModel, {
      where: { id, companyId },
    });
    if (!bankInfo) throw new NotFoundException('Bank info not found');
    return bankInfo as BankInfo;
  }

  async update(
    id: number,
    dto: UpdateBankInfoDto,
    companyId: number,
  ): Promise<BankInfo> {
    // Only update if the record belongs to the company
    const bankInfo = await this.crudHelperService.findOne(this.bankInfoModel, {
      where: { id, companyId },
    });
    if (!bankInfo) throw new NotFoundException('Bank info not found');
    await this.crudHelperService.update(this.bankInfoModel, dto, {
      where: { id, companyId },
    });
    // Return the updated record
    return (await this.crudHelperService.findOne(this.bankInfoModel, {
      where: { id, companyId },
    })) as BankInfo;
  }

  async remove(id: number, companyId: number): Promise<void> {
    // Only delete if the record belongs to the company
    const bankInfo = await this.crudHelperService.findOne(this.bankInfoModel, {
      where: { id, companyId },
    });
    if (!bankInfo) throw new NotFoundException('Bank info not found');
    await this.crudHelperService.delete(this.bankInfoModel, {
      where: { id, companyId },
    });
  }

  async findByUserId(userId: number, companyId: number): Promise<BankInfo> {
    // Only return if the user belongs to the company
    const user = await this.crudHelperService.findOne(this.userModel, {
      where: { id: userId, companyId },
    });
    if (!user)
      throw new NotFoundException('User does not belong to your company');
    const bankInfo = await this.crudHelperService.findOne(this.bankInfoModel, {
      where: { userId, companyId },
    });
    if (!bankInfo) throw new NotFoundException('Bank info not found');
    return bankInfo as BankInfo;
  }
}
