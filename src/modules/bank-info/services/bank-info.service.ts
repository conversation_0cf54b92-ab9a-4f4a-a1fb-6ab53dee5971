import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { BankInfo } from '../../../models/users-models/bank-info.model';
import { CreateBankInfoDto } from '../dto/create-bank-info.dto';
import { UpdateBankInfoDto } from '../dto/update-bank-info.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { TenantAwareBaseService } from '../../../common/services/tenant-aware-base.service';
import { TenantContextI } from '../../../common/types/tenant-context.types';
import { User } from 'src/models/users-models/user.model';

@Injectable()
export class BankInfoService extends TenantAwareBaseService {
  constructor(
    @InjectModel(BankInfo)
    private bankInfoModel: typeof BankInfo,
    crudHelperService: CrudHelperService,
    @InjectModel(User)
    private userModel: typeof User,
  ) {
    super(crudHelperService);
  }

  async create(dto: CreateBankInfoDto): Promise<BankInfo> {
    return this.bankInfoModel.create({ ...dto });
  }

  async findOne(id: number, tenantContext: TenantContextI): Promise<BankInfo> {
    const record = await this.findOneWithTenantFiltering(
      this.bankInfoModel,
      id,
      tenantContext,
      { model: BankInfo, userRelation: 'user' },
    );
    if (!record) throw new NotFoundException('Not found or not authorized');
    return record;
  }

  async update(
    id: number,
    dto: UpdateBankInfoDto,
    tenantContext: TenantContextI,
  ): Promise<BankInfo> {
    const record = await this.validateTenantAccess(tenantContext, id, {
      model: BankInfo,
    });
    if (!record) throw new NotFoundException('Not authorized');
    await record.update(dto);
    return record;
  }

  async remove(id: number, tenantContext: TenantContextI): Promise<void> {
    const record = await this.validateTenantAccess(tenantContext, id, {
      model: BankInfo,
    });
    if (!record) throw new NotFoundException('Not authorized');
    await record.destroy();
  }

  async findByUserId(
    userId: number,
    tenantContext: TenantContextI,
  ): Promise<BankInfo> {
    // Check if user belongs to the same company as tenantContext
    const user = await this.findOneWithTenantFiltering(
      this.userModel,
      userId,
      tenantContext,
      { model: User },
    );
    if (!user) {
      throw new NotFoundException('User does not belong to your company');
    }
    const bankInfo = await this.bankInfoModel.findOne({ where: { userId } });
    if (!bankInfo) {
      throw new NotFoundException('Bank info not found');
    }
    return bankInfo;
  }
}
