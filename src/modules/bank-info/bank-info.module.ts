import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { BankInfo } from '../../models/users-models/bank-info.model';
import { User } from '../../models/users-models/user.model';
import { BankInfoService } from './services/bank-info.service';
import { BankInfoController } from './controllers/bank-info.controller';

@Module({
  imports: [SequelizeModule.forFeature([BankInfo, User])],
  providers: [BankInfoService],
  controllers: [BankInfoController],
  exports: [BankInfoService],
})
export class BankInfoModule {}
