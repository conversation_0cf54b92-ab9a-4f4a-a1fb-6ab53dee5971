import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Req,
  Put,
} from '@nestjs/common';
import { BankInfoService } from '../services/bank-info.service';
import { CreateBankInfoDto } from '../dto/create-bank-info.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
  AuthenticatedOnly,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { UpdateBankInfoDto } from '../dto/update-bank-info.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { BankInfo } from 'src/models/users-models/bank-info.model';

@Controller('bank-info')
@UseGuards(PermissionGuard)
export class BankInfoController {
  constructor(private readonly bankInfoService: BankInfoService) {}

  @Post()
  @AuthenticatedOnly()
  async create(
    @Body() dto: CreateBankInfoDto,
    @Req() req: UserRequestI,
  ): Promise<Partial<BankInfo>> {
    return this.bankInfoService.create(dto, req.user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    return this.bankInfoService.findOne(id, req.user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateBankInfoDto,
    @Req() req: UserRequestI,
  ) {
    return this.bankInfoService.update(id, dto, req.user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    return this.bankInfoService.remove(id, req.user.companyId);
  }

  @Get('user/:userId')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findByUserId(
    @Param('userId', ParseIntPipe) userId: number,
    @Req() req: UserRequestI,
  ) {
    return this.bankInfoService.findByUserId(userId, req.user.companyId);
  }
}
