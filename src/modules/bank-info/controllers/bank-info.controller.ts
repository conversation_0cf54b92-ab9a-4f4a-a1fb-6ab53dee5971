import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Req,
  Put,
} from '@nestjs/common';
import { BankInfoService } from '../services/bank-info.service';
import { CreateBankInfoDto } from '../dto/create-bank-info.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { UpdateBankInfoDto } from '../dto/update-bank-info.dto';
import { TenantContextI } from 'src/common/types/tenant-context.types';
import { TenantContext } from 'src/common/decorators/tenant-context.decorator';

@Controller('bank-info')
@UseGuards(PermissionGuard)
export class BankInfoController {
  constructor(private readonly bankInfoService: BankInfoService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async create(@Body() dto: CreateBankInfoDto) {
    return this.bankInfoService.create(dto);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findOne(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    return this.bankInfoService.findOne(id, req.tenantContext);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateBankInfoDto,
    @TenantContext() tenantContext: TenantContextI,
  ) {
    return this.bankInfoService.update(id, dto, tenantContext);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async remove(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    return this.bankInfoService.remove(id, req.tenantContext);
  }

  @Get('user/:userId')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findByUserId(
    @Param('userId', ParseIntPipe) userId: number,
    @Req() req: any,
  ) {
    return this.bankInfoService.findByUserId(userId, req.tenantContext);
  }
}
