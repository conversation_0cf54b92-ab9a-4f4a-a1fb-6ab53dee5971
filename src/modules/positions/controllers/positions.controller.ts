import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Req,
  Put,
} from '@nestjs/common';
import { PositionsService } from '../services/positions.service';
import { CreatePositionDto } from '../dto/create-position.dto';
import { UpdatePositionDto } from '../dto/update-position.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
// import { GetUser } from '../../auth/decorators/get-user.decorator';
import { RequestUserObjectI, UserRequestI } from '../../auth/auth.interfaces';

@UseGuards(JwtAuthGuard)
@Controller('positions')
export class PositionsController {
  constructor(private readonly positionsService: PositionsService) {}

  @Post()
  create(
    @Body() createPositionDto: CreatePositionDto,
    @Req() req: UserRequestI,
  ) {
    const companyId = req.user.companyId;
    return this.positionsService.create(createPositionDto, companyId);
  }

  @Get()
  findAll(@Req() req: UserRequestI) {
    const companyId = req.user.companyId;
    return this.positionsService.findAll(companyId);
  }

  @Get('department/:departmentId')
  findByDepartment(
    @Param('departmentId', ParseIntPipe) departmentId: number,
    user: RequestUserObjectI,
  ) {
    return this.positionsService.findByDepartment(departmentId, user);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePositionDto: UpdatePositionDto,
    @Req() req: UserRequestI,
    user: RequestUserObjectI,
  ) {
    const companyId = req.user.companyId;
    return this.positionsService.update(id, updatePositionDto, companyId);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number, @Req() req: UserRequestI) {
    const companyId = req.user.companyId;
    return this.positionsService.remove(id, companyId);
  }
}
