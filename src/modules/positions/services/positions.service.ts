import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Position } from '../../../models/position.model';
import { CreatePositionDto } from '../dto/create-position.dto';
import { UpdatePositionDto } from '../dto/update-position.dto';
import { Department } from '../../../models/department.model';
import { Company } from '../../../models/company.model';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';

@Injectable()
export class PositionsService {
  constructor(
    @InjectModel(Position)
    private readonly positionModel: typeof Position,
    private readonly crudHelperService: CrudHelperService,
  ) {}

  async create(
    createPositionDto: CreatePositionDto,
    companyId: number,
  ): Promise<Position> {
    return (await this.crudHelperService.createWithCompany(
      this.positionModel,
      createPositionDto,
      companyId,
    )) as Position;
  }

  async findAll(companyId: number): Promise<Position[]> {
    return (await this.crudHelperService.findAllWithCompany(
      this.positionModel,
      companyId,
      {
        include: [
          {
            model: Department,
            attributes: ['id', 'name'],
          },
          {
            model: Company,
            attributes: ['id', 'name'],
          },
        ],
        order: [['title', 'ASC']],
      },
    )) as Position[];
  }

  async findOne(id: number, companyId: number): Promise<Position> {
    const position = (await this.crudHelperService.findOneWithCompany(
      this.positionModel,
      companyId,
      {
        where: { id },
        include: [
          {
            model: Department,
            attributes: ['id', 'name'],
          },
          {
            model: Company,
            attributes: ['id', 'name'],
          },
        ],
      },
    )) as Position;

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    return position;
  }

  async update(
    id: number,
    updatePositionDto: UpdatePositionDto,
    companyId: number,
  ): Promise<Position> {
    const position = await this.findOne(id, companyId);

    await position.update(updatePositionDto);
    return position;
  }

  async remove(id: number, companyId: number): Promise<void> {
    const position = await this.findOne(id, companyId);
    await position.destroy();
  }

  async findByDepartment(
    departmentId: number,
    companyId: number,
  ): Promise<Position[]> {
    return (await this.crudHelperService.findAllWithCompany(
      this.positionModel,
      companyId,
      {
        where: { departmentId },
        include: [
          {
            model: Department,
            attributes: ['id', 'name'],
          },
        ],
        order: [['title', 'ASC']],
      },
    )) as Position[];
  }

  async findActivePositions(companyId: number): Promise<Position[]> {
    return (await this.crudHelperService.findAllWithCompany(
      this.positionModel,
      companyId,
      {
        include: [
          {
            model: Department,
            attributes: ['id', 'name'],
          },
        ],
        order: [['title', 'ASC']],
      },
    )) as Position[];
  }
}
