import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Position } from '../../../models/position.model';
import { CreatePositionDto } from '../dto/create-position.dto';
import { UpdatePositionDto } from '../dto/update-position.dto';
import { Department } from '../../../models/department.model';
import { Company } from '../../../models/company.model';
import { RequestUserObjectI } from '../../auth/auth.interfaces';

@Injectable()
export class PositionsService {
  constructor(
    @InjectModel(Position)
    private readonly positionModel: typeof Position,
  ) {}

  async create(
    createPositionDto: CreatePositionDto,
    companyId: number,
  ): Promise<Position> {
    const positionData = {
      ...createPositionDto,
      companyId,
    };

    return await this.positionModel.create(positionData);
  }

  async findAll(companyId: number): Promise<Position[]> {
    return await this.positionModel.findAll({
      where: { companyId },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
        {
          model: Company,
          attributes: ['id', 'name'],
        },
      ],
      order: [['title', 'ASC']],
    });
  }

  async findOne(id: number, companyId: number): Promise<Position> {
    const position = await this.positionModel.findOne({
      where: {
        id,
        companyId,
      },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
        {
          model: Company,
          attributes: ['id', 'name'],
        },
      ],
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    return position;
  }

  async update(
    id: number,
    updatePositionDto: UpdatePositionDto,
    companyId: number,
  ): Promise<Position> {
    const position = await this.findOne(id, companyId);

    await position.update(updatePositionDto);
    return position;
  }

  async remove(id: number, companyId: number): Promise<void> {
    const position = await this.findOne(id, companyId);
    await position.destroy();
  }

  async findByDepartment(
    departmentId: number,
    user: RequestUserObjectI,
  ): Promise<Position[]> {
    return await this.positionModel.findAll({
      where: {
        departmentId,
        // companyId: user.companyId,
        isActive: true,
      },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
      ],
      order: [['title', 'ASC']],
    });
  }

  async findActivePositions(user: RequestUserObjectI): Promise<Position[]> {
    return await this.positionModel.findAll({
      where: {
        // companyId: user.companyId,
        isActive: true,
      },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
      ],
      order: [['title', 'ASC']],
    });
  }
}
