import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PositionsController } from './controllers/positions.controller';
import { PositionsService } from './services/positions.service';
import { Position } from '../../models/position.model';

@Module({
  imports: [SequelizeModule.forFeature([Position])],
  controllers: [PositionsController],
  providers: [PositionsService],
  exports: [PositionsService],
})
export class PositionsModule {}
