import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from '../roles.service';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { UserFindAllRolesDto } from '../dto/user.find.all.dto';
import { SetPermissionsDto } from '../dto/set-permissions.dto';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { FindAllRolesDto } from '../dto/find.all.dto';

@Controller('roles')
@UseGuards(PermissionGuard)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  findAll(@Req() req: UserRequestI, @Query() dto: FindAllRolesDto) {
    return this.rolesService.findAllCompanyRoles(req.user, dto);
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  findAllPaginated(
    @Req() req: UserRequestI,
    @Query() dto: UserFindAllRolesDto,
  ) {
    return this.rolesService.findAllByUserPaginated(req.user, dto);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  findById(@Param('id', ParseIntPipe) id: number, @Req() req: UserRequestI) {
    return this.rolesService.findById(id, req.user);
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.SETTINGS)
  create(@Body() dto: Partial<Role>, @Req() req: UserRequestI) {
    return this.rolesService.create(dto, req.user);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.SETTINGS)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: Partial<Role>,
    @Req() req: UserRequestI,
  ) {
    return this.rolesService.update(id, dto, req.user);
  }

  @Put(':id/permissions')
  @CanEdit(PLATFORM_SECTION_ENUM.SETTINGS)
  setPermissions(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: SetPermissionsDto,
    @Req() req: UserRequestI,
  ) {
    return this.rolesService.setPermissions(id, dto, req.user);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.SETTINGS)
  delete(@Param('id', ParseIntPipe) id: number, @Req() req: UserRequestI) {
    return this.rolesService.delete(id, req.user);
  }
}
