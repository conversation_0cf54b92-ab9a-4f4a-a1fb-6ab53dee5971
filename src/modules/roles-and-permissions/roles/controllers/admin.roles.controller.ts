import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from '../roles.service';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { AdminRequestI } from 'src/modules/auth/auth.interfaces';
import { FindAllRolesDto } from '../dto/find.all.dto';
import { SetPermissionsDto } from '../dto/set-permissions.dto';
import {
  CanCreatePlatform,
  CanDeletePlatform,
  CanViewPlatform,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { CanEditPlatform } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';

@Controller('admin/roles')
@UseGuards(PermissionGuard)
export class AdminRolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get()
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  findAll(@Query() dto: FindAllRolesDto) {
    return this.rolesService.findAllPlatformRoles(dto);
  }

  @Get('paginated')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  findAllPaginated(@Query() dto: FindAllRolesDto) {
    return this.rolesService.findAllPlatformRolesPaginated(dto);
  }

  @Get(':id')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.findById(id);
  }

  @Post()
  @CanCreatePlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  create(@Body() dto: Partial<Role>) {
    return this.rolesService.create(dto);
  }

  @Put(':id')
  @CanEditPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  update(@Param('id', ParseIntPipe) id: number, @Body() dto: Partial<Role>) {
    return this.rolesService.update(id, dto);
  }

  @Put(':id/permissions')
  @CanEditPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  setPermissions(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: SetPermissionsDto,
  ) {
    return this.rolesService.setPermissions(id, dto);
  }

  @Delete(':id')
  @CanDeletePlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.delete(id);
  }
}
