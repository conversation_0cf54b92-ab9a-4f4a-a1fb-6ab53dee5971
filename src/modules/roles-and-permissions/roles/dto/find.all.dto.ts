import { Is<PERSON>num, <PERSON>N<PERSON>ber, <PERSON><PERSON>ptional, <PERSON>String, Min } from 'class-validator';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { Type } from 'class-transformer';

export class FindAllRolesDto {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  limit?: number = 10;
}
