import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { PermissionLevelEnum } from '../../permissions/enums/enum';

export class SetPermissionsDto {
  @IsEnum(PermissionLevelEnum)
  @IsOptional()
  level: PermissionLevelEnum;

  @IsInt()
  @IsNotEmpty()
  platformSectionId: number;

  @IsBoolean()
  @IsOptional()
  canView?: boolean = false;

  @IsBoolean()
  @IsOptional()
  canCreate?: boolean = false;

  @IsBoolean()
  @IsOptional()
  canEdit?: boolean = false;

  @IsBoolean()
  @IsOptional()
  canDelete?: boolean = false;
}
