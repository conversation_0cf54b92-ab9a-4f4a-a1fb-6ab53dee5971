import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CrudHelperService } from 'src/common/crud-helper/crud-helper.service';
import {
  PlatformSection,
  PlatformSectionScopeEnum,
} from 'src/models/platform-section.model';

@Injectable()
export class PlatformSectionsService {
  constructor(
    @InjectModel(PlatformSection)
    private readonly platformSectionModel: typeof PlatformSection,
    private readonly crudService: CrudHelperService,
  ) {}

  create(data: Partial<PlatformSection>) {
    return this.crudService.create(this.platformSectionModel, data);
  }

  async findAll(): Promise<Partial<PlatformSection>[]> {
    const sections = await this.crudService.findAll(this.platformSectionModel, {
      where: {
        scope: [
          PlatformSectionScopeEnum.PLATFORM_ONLY,
          PlatformSectionScopeEnum.BOTH,
        ],
      },
      order: [['order', 'ASC']],
    });

    return sections.map((section) => section.get({ plain: true }));
  }

  async findOnlyCompanySections(): Promise<Partial<PlatformSection>[]> {
    const sections = await this.crudService.findAll(this.platformSectionModel, {
      where: {
        scope: [
          PlatformSectionScopeEnum.COMPANY_ONLY,
          PlatformSectionScopeEnum.BOTH,
        ],
      },
      order: [['order', 'ASC']],
    });

    // crudService.findAll returns plain objects, so no need for .get()
    return sections as Partial<PlatformSection>[];
  }

  async findById(id: number): Promise<Partial<PlatformSection>> {
    const section = await this.crudService.findOne(this.platformSectionModel, {
      where: { id },
    });

    if (!section) {
      return null;
    }

    return section.get({ plain: true });
  }

  update(id: number, data: Partial<PlatformSection>) {
    return this.crudService.update(this.platformSectionModel, data, {
      where: { id },
    });
  }

  delete(id: number) {
    return this.crudService.delete(this.platformSectionModel, {
      where: { id },
    });
  }
}
