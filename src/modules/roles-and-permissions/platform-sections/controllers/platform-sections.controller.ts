import {
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { PlatformSectionsService } from '../platform-sections.service';
import { PlatformSection } from 'src/models/platform-section.model';

@Controller('platform-sections')
export class PlatformSectionsController {
  constructor(
    private readonly platformSectionsService: PlatformSectionsService,
  ) {}

  @Get()
  async findAll(): Promise<{
    message: string;
    data: Partial<PlatformSection>[];
  }> {
    const allSections =
      await this.platformSectionsService.findOnlyCompanySections();

    return {
      message: 'Platform sections fetched successfully',
      data: allSections,
    };
  }

  @Get(':id')
  async findById(@Param('id', ParseIntPipe) id: number): Promise<{
    message: string;
    data: Partial<PlatformSection>;
  }> {
    const section = await this.platformSectionsService.findById(id);

    if (!section) {
      throw new NotFoundException('Platform section not found');
    }

    return {
      message: 'Platform section fetched successfully',
      data: section.get({ plain: true }),
    };
  }
}
