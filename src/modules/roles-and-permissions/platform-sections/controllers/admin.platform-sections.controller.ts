import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { PlatformSectionsService } from '../platform-sections.service';
import { CreatePlatformSectionDto } from '../dto/create-platform-section.dto';
import { UpdatePlatformSectionDto } from '../dto/update-platform-section.dto';

@Controller('admin/platform-sections')
export class AdminPlatformSectionsController {
  constructor(
    private readonly platformSectionsService: PlatformSectionsService,
  ) {}

  @Get()
  findAll() {
    return this.platformSectionsService.findAll();
  }

  @Get(':id')
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.platformSectionsService.findById(id);
  }

  @Post()
  create(@Body() dto: CreatePlatformSectionDto) {
    return this.platformSectionsService.create(dto);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdatePlatformSectionDto,
  ) {
    return this.platformSectionsService.update(id, dto);
  }

  @Delete(':id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.platformSectionsService.delete(id);
  }
}
