import { Global, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PlatformSection } from 'src/models/platform-section.model';
import { AdminPlatformSectionsController } from './controllers/admin.platform-sections.controller';
import { PlatformSectionsController } from './controllers/platform-sections.controller';
import { PlatformSectionsService } from './platform-sections.service';

@Global()
@Module({
  imports: [SequelizeModule.forFeature([PlatformSection])],
  controllers: [AdminPlatformSectionsController, PlatformSectionsController],
  providers: [PlatformSectionsService],
  exports: [PlatformSectionsService],
})
export class PlatformSectionsModule {}
