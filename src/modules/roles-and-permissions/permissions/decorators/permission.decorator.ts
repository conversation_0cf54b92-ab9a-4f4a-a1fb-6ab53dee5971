import { SetMetadata } from '@nestjs/common';
import {
  IPermissionDecoratorOptions,
  PermissionAction,
  PermissionScope,
} from '../../../auth/auth.interfaces';
import {
  PERMISSION_ACTION_ENUM,
  ROLE_SCOPE_ENUM,
} from '../../../../utils/enums';

// TODO: Move it to the config file and env
export const PERMISSION_KEY = 'permissions';
export const PUBLIC_ROUTE_KEY = 'isPublicRoute';

/**
 * Main permission decorator - simplified and clean
 * @param section Platform section (e.g., 'users', 'roles-and-permissions')
 * @param action Permission action ('canView', 'canCreate', 'canEdit', 'canDelete')
 * @param scope Permission scope ('platform' or 'company') - defaults to 'company'
 */
export const RequirePermission = (
  section: string,
  action: PermissionAction = PERMISSION_ACTION_ENUM.VIEW,
  scope: PermissionScope = ROLE_SCOPE_ENUM.COMPANY,
): MethodDecorator => {
  const permissionOptions: IPermissionDecoratorOptions = {
    section,
    action,
    scope,
  };

  return SetMetadata(PERMISSION_KEY, permissionOptions);
};

/**
 * Company-scoped permission decorators (default scope)
 */
export const CanView = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.VIEW,
    ROLE_SCOPE_ENUM.COMPANY,
  );

export const CanCreate = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.CREATE,
    ROLE_SCOPE_ENUM.COMPANY,
  );

export const CanEdit = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.EDIT,
    ROLE_SCOPE_ENUM.COMPANY,
  );

export const CanDelete = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.DELETE,
    ROLE_SCOPE_ENUM.COMPANY,
  );

/**
 * Platform-scoped permission decorators (admin only)
 */
export const CanViewPlatform = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.VIEW,
    ROLE_SCOPE_ENUM.PLATFORM,
  );

export const CanCreatePlatform = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.CREATE,
    ROLE_SCOPE_ENUM.PLATFORM,
  );

export const CanEditPlatform = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.EDIT,
    ROLE_SCOPE_ENUM.PLATFORM,
  );

export const CanDeletePlatform = (section: string) =>
  RequirePermission(
    section,
    PERMISSION_ACTION_ENUM.DELETE,
    ROLE_SCOPE_ENUM.PLATFORM,
  );

/**
 * Marks a route as public - no authentication or permission required
 * Use this for routes like health checks, public documentation, etc.
 */
export const PublicRoute = (): MethodDecorator =>
  SetMetadata(PUBLIC_ROUTE_KEY, true);

/**
 * Marks a route as authentication only - no permission required
 * User must be authenticated but no role/permission checks are performed
 */
export const AuthenticatedOnly = (): MethodDecorator =>
  SetMetadata(PERMISSION_KEY, null);
