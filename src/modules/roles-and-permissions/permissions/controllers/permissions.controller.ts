import {
  Controller,
  Get,
  Post,
  Body,
  Request,
  HttpStatus,
  HttpCode,
  UseGuards,
} from '@nestjs/common';
import { CanView } from '../decorators/permission.decorator';
import { PermissionService } from '../permission.service';
import { IUserPermissions } from 'src/modules/auth/auth.interfaces';
import { IPermissionCheck } from 'src/modules/auth/auth.interfaces';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { PlatformSection } from 'src/models/platform-section.model';

@Controller('permissions')
@UseGuards(PermissionGuard)
export class PermissionsController {
  constructor(private readonly permissionService: PermissionService) {}

  /**
   * Get current user's permissions
   */
  @Get('my-permissions')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getMyPermissions(
    @Request() req: any,
  ): Promise<IUserPermissions | null> {
    const userId = req.user.id;
    return this.permissionService.getUserPermissions(userId);
  }

  /**
   * Get current user's accessible sections
   */
  @Get('accessible-sections')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getAccessibleSections(@Request() req: any): Promise<string[]> {
    const userId = req.user.id;
    return this.permissionService.getUserAccessibleSections(userId);
  }

  /**
   * Check if user has specific permission
   */
  @Post('check')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async checkPermission(
    @Request() req: any,
    @Body() permissionCheck: IPermissionCheck,
  ): Promise<{ hasPermission: boolean }> {
    const userId = req.user.id;
    const hasPermission = await this.permissionService.checkPermission(
      userId,
      permissionCheck.section,
      permissionCheck.action,
      permissionCheck.companyId,
    );

    return { hasPermission };
  }

  /**
   * Check multiple permissions at once
   */
  @Post('check-multiple')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async checkMultiplePermissions(
    @Request() req: any,
    @Body() permissionChecks: IPermissionCheck[],
  ): Promise<{ [key: string]: boolean }> {
    const userId = req.user.id;
    return this.permissionService.checkMultiplePermissions(
      userId,
      permissionChecks,
    );
  }

  /**
   * Get user's company ID
   */
  @Get('company-id')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getCompanyId(
    @Request() req: any,
  ): Promise<{ companyId: number | null }> {
    const userId = req.user.id;
    const companyId = await this.permissionService.getUserCompanyId(userId);
    return { companyId };
  }

  /**
   * Check if user has platform scope
   */
  @Get('platform-scope')
  // @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async hasPlatformScope(
    @Request() req: any,
  ): Promise<{ hasPlatformScope: boolean }> {
    const userId = req.user.id;
    const hasPlatformScope =
      await this.permissionService.hasPlatformScope(userId);
    return { hasPlatformScope };
  }

  /**
   * Get all available platform sections
   */
  @Get('platform-sections')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getPlatformSections(): Promise<{ sections: string[] }> {
    const sections = Object.values(PLATFORM_SECTION_ENUM);
    return { sections };
  }
}
