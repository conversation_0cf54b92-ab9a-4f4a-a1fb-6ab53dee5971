import { Global, Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { PlatformSection } from 'src/models/platform-section.model';
import { Company } from 'src/models/company.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { AdminPermissionsController } from './controllers/admin.permissions.controller';
import { PermissionsController } from './controllers/permissions.controller';
import { PermissionService } from './permission.service';
import { CrudHelperService } from 'src/common/crud-helper/crud-helper.service';
import { RolesModule } from '../roles/roles.module';
import { RedisCacheService } from '../../../common/services/redis-cache.service';
import { ConfigModule } from '@nestjs/config';

@Global()
@Module({
  imports: [
    SequelizeModule.forFeature([
      Permission,
      UserRole,
      Role,
      PlatformSection,
      Company,
      EmploymentDetails,
    ]),
    forwardRef(() => RolesModule),
    ConfigModule,
  ],
  controllers: [AdminPermissionsController, PermissionsController],
  providers: [PermissionService, CrudHelperService, RedisCacheService],
  exports: [PermissionService],
})
export class PermissionsModule {}
