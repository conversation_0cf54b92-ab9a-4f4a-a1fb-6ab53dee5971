import { Global, Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { UserRoleService } from './user-role.service';
import { AdminUserRoleController } from './controllers/admin.user.role.controller';
import { UserRoleController } from './controllers/user-role.controller';
import { PermissionsModule } from 'src/modules/roles-and-permissions/permissions/permissions.module';

@Global()
@Module({
  imports: [
    SequelizeModule.forFeature([UserRole, Role]),
    forwardRef(() => PermissionsModule),
  ],
  controllers: [AdminUserRoleController, UserRoleController],
  providers: [UserRoleService],
  exports: [UserRoleService],
})
export class UserRoleModule {}
