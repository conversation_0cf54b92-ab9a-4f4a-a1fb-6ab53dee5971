import {
  Controller,
  Get,
  Param,
  Body,
  Delete,
  Put,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UserRoleService } from '../user-role.service';
import { AssignRoleDto } from '../dto/assign-role.dto';

@Controller('admin/user-roles')
export class AdminUserRoleController {
  constructor(private readonly userRoleService: UserRoleService) {}

  @Put('assign')
  assignRole(@Body() body: AssignRoleDto) {
    return this.userRoleService.assignRole(body.userId, body.roleId);
  }

  @Get('user/:userId')
  getRoleForUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.userRoleService.getRoleForUser(userId);
  }

  @Get('paginate')
  async getAllPaginated(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.userRoleService.getAllPaginated(Number(page), Number(limit));
  }

  @Delete(':id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.userRoleService.delete(id);
  }
}
