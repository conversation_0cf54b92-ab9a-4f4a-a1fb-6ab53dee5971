import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { NotificationsService } from '../services/notifications.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Notification } from '../../../models/notification.model';

@Controller('api/notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  async create(@Body() createNotificationDto: any): Promise<Notification> {
    return this.notificationsService.create(createNotificationDto);
  }

  @Get()
  async findAll(): Promise<Notification[]> {
    return this.notificationsService.findAll();
  }

  @Get('recipient/:recipientId')
  async findByRecipientId(
    @Param('recipientId') recipientId: number,
  ): Promise<Notification[]> {
    return this.notificationsService.findByRecipientId(recipientId);
  }

  @Get('unread/:recipientId')
  async findUnreadByRecipientId(
    @Param('recipientId') recipientId: number,
  ): Promise<Notification[]> {
    return this.notificationsService.findUnreadByRecipientId(recipientId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Notification> {
    return this.notificationsService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateNotificationDto: any,
  ): Promise<Notification> {
    return this.notificationsService.update(id, updateNotificationDto);
  }

  @Patch('mark-read/:id')
  async markAsRead(@Param('id') id: number): Promise<Notification> {
    return this.notificationsService.markAsRead(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.notificationsService.remove(id);
  }
}
