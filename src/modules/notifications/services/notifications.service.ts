import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Notification } from '../../../models/notification.model';
import { NotificationSetting } from '../../../models/notification-setting.model';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notification)
    private notificationModel: typeof Notification,
    @InjectModel(NotificationSetting)
    private notificationSettingModel: typeof NotificationSetting,
  ) {}

  async create(createNotificationDto: any): Promise<Notification> {
    return this.notificationModel.create({ ...createNotificationDto });
  }

  async findAll(): Promise<Notification[]> {
    return this.notificationModel.findAll({
      include: { all: true },
    });
  }

  async findByRecipientId(recipientId: number): Promise<Notification[]> {
    return this.notificationModel.findAll({
      where: { recipientId },
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  async findUnreadByRecipientId(recipientId: number): Promise<Notification[]> {
    return this.notificationModel.findAll({
      where: { recipientId, isRead: false },
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<Notification> {
    return this.notificationModel.findByPk(id, {
      include: { all: true },
    });
  }

  async update(id: number, updateNotificationDto: any): Promise<Notification> {
    const notification = await this.findOne(id);
    await notification.update(updateNotificationDto);
    return this.findOne(id);
  }

  async markAsRead(id: number): Promise<Notification> {
    const notification = await this.findOne(id);
    await notification.update({ isRead: true });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const notification = await this.findOne(id);
    await notification.destroy();
  }

  async getNotificationSettings(): Promise<NotificationSetting[]> {
    return this.notificationSettingModel.findAll();
  }

  async updateNotificationSetting(
    id: number,
    data: any,
  ): Promise<NotificationSetting> {
    const setting = await this.notificationSettingModel.findByPk(id);
    await setting.update(data);
    return setting;
  }
}
