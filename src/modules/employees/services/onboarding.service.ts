import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { OnboardingData } from '../../../models/onboarding-data.model';
import { CreateOnboardingDto } from '../dto/create-onboarding-dto';
import { NotificationsService } from '../../notifications/services/notifications.service';

@Injectable()
export class OnboardingService {
  constructor(
    @InjectModel(OnboardingData)
    private onboardingDataModel: typeof OnboardingData,
    private notificationsService: NotificationsService,
  ) {}

  async create(
    createOnboardingDto: CreateOnboardingDto,
  ): Promise<OnboardingData> {
    // Set completion date if all required elements are present
    const completionDate = new Date();

    const onboardingData = await this.onboardingDataModel.create({
      employeeId: createOnboardingDto.employeeId,
      personalInfo: createOnboardingDto.personalInfo,
      emergencyContacts: createOnboardingDto.emergencyContacts,
      policiesAccepted: createOnboardingDto.policiesAccepted,
      documents: createOnboardingDto.documents || {},
      qualifications: createOnboardingDto.qualifications || {},
      status: 'completed',
      completedSteps: 5, // Assuming 5 is the total number of steps
      completionDate,
    });

    // Create notification for HR
    await this.notificationsService.create({
      userId: 1, // Admin/HR user ID
      type: 'onboarding',
      title: 'Onboarding Completed',
      message: `Employee ${createOnboardingDto.personalInfo.firstName} ${createOnboardingDto.personalInfo.lastName} has completed the onboarding process.`,
      isRead: false,
    });

    return onboardingData;
  }

  async findByEmployeeId(employeeId: number): Promise<OnboardingData> {
    return this.onboardingDataModel.findOne({
      where: { employeeId },
    });
  }

  async update(
    id: number,
    updateOnboardingDto: Partial<CreateOnboardingDto>,
  ): Promise<OnboardingData> {
    const onboardingData = await this.onboardingDataModel.findByPk(id);

    if (!onboardingData) {
      throw new Error('Onboarding data not found');
    }

    // Update fields
    if (updateOnboardingDto.personalInfo) {
      onboardingData.personalInfo = {
        ...onboardingData.personalInfo,
        ...updateOnboardingDto.personalInfo,
      };
    }

    if (updateOnboardingDto.emergencyContacts) {
      onboardingData.emergencyContacts = updateOnboardingDto.emergencyContacts;
    }

    if (updateOnboardingDto.documents) {
      onboardingData.documents = {
        ...onboardingData.documents,
        ...updateOnboardingDto.documents,
      };
    }

    if (updateOnboardingDto.policiesAccepted) {
      onboardingData.policiesAccepted = {
        ...onboardingData.policiesAccepted,
        ...updateOnboardingDto.policiesAccepted,
      };
    }

    if (updateOnboardingDto.qualifications) {
      onboardingData.qualifications = {
        ...onboardingData.qualifications,
        ...updateOnboardingDto.qualifications,
      };
    }

    await onboardingData.save();
    return onboardingData;
  }
}
