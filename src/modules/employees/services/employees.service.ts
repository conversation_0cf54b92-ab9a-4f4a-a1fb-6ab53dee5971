import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { User } from '../../../models/users-models/user.model';

@Injectable()
export class EmployeesService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async findAll(): Promise<User[]> {
    return this.userModel.findAll();
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userModel.findByPk(id);

    if (!user) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    return user;
  }

  async findByUserId(userId: number): Promise<User> {
    const user = await this.userModel.findOne({
      where: { userId },
    });

    if (!user) {
      throw new NotFoundException(`Employee with user ID ${userId} not found`);
    }

    return user;
  }

  async create(createEmployeeDto: any): Promise<User> {
    return this.userModel.create(createEmployeeDto);
  }

  async update(id: number, updateEmployeeDto: any): Promise<User> {
    const employee = await this.findOne(id);
    await employee.update(updateEmployeeDto);
    return employee;
  }

  async remove(id: number): Promise<void> {
    const employee = await this.findOne(id);
    await employee.destroy();
  }
}
