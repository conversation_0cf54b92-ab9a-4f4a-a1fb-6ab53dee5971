import {
  Is<PERSON>mail,
  Is<PERSON>num,
  IsNot<PERSON>mpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  Min<PERSON><PERSON><PERSON>,
} from 'class-validator';
import { GENDER_TYPES_ENUM } from 'src/utils/enums';

export class CreateEmployeeDto {
  @IsNotEmpty()
  @IsString()
  username: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsEnum(GENDER_TYPES_ENUM)
  gender: GENDER_TYPES_ENUM;

  @IsNotEmpty()
  @IsNumber()
  companyId: number;

  @IsNotEmpty()
  @IsNumber()
  nationalityId: number;

  @IsNotEmpty()
  @IsNumber()
  departmentId: number;

  @IsNotEmpty()
  @IsNumber()
  roleId: number;
}
