import { IsNotEmpty, IsObject, IsOptional } from 'class-validator';

export class CreateOnboardingDto {
  @IsNotEmpty()
  employeeId: number;

  @IsNotEmpty()
  @IsObject()
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dob: string;
    gender?: string;
    address: string;
    city: string;
    country: string;
    postalCode?: string;
    nationality?: string;
  };

  @IsNotEmpty()
  @IsObject()
  emergencyContacts: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  }[];

  @IsOptional()
  @IsObject()
  documents?: {
    identification?: boolean;
    resume?: boolean;
    education?: boolean;
  };

  @IsNotEmpty()
  @IsObject()
  policiesAccepted: {
    codeOfConduct: boolean;
    confidentiality: boolean;
    dataProtection: boolean;
    signatureProvided: boolean;
  };

  @IsOptional()
  @IsObject()
  qualifications?: any;
}
