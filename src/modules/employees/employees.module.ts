import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { OnboardingData } from '../../models/onboarding-data.model';
import { EmployeesController } from './controllers/employees.controller';
import { OnboardingController } from './controllers/onboarding.controller';
import { EmployeesService } from './services/employees.service';
import { OnboardingService } from './services/onboarding.service';
import { NotificationsModule } from '../notifications/notifications.module';
import { PermissionsModule } from '../roles-and-permissions/permissions/permissions.module';
import { User } from 'src/models/users-models/user.model';

@Module({
  imports: [
    SequelizeModule.forFeature([User, OnboardingData]),
    NotificationsModule,
    PermissionsModule,
  ],
  controllers: [EmployeesController, OnboardingController],
  providers: [EmployeesService, OnboardingService],
  exports: [EmployeesService, OnboardingService],
})
export class EmployeesModule {}
