import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { OnboardingService } from '../services/onboarding.service';
import { OnboardingData } from '../../../models/onboarding-data.model';
import { CreateOnboardingDto } from '../dto/create-onboarding-dto';
// Uncomment when auth guard is ready
// import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('employee')
export class OnboardingController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Post('onboarding')
  // @UseGuards(JwtAuthGuard) - Uncomment when auth is implemented
  async create(@Body() createOnboardingDto: CreateOnboardingDto): Promise<any> {
    try {
      const onboardingData =
        await this.onboardingService.create(createOnboardingDto);

      return {
        success: true,
        message: 'Onboarding data saved successfully',
        onboardingId: onboardingData.id,
        userId: onboardingData.userId,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to save onboarding data',
        error: error.message,
      };
    }
  }

  @Get('onboarding/:employeeId')
  // @UseGuards(JwtAuthGuard) - Uncomment when auth is implemented
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<OnboardingData> {
    return this.onboardingService.findByEmployeeId(employeeId);
  }

  @Patch('onboarding/:id')
  // @UseGuards(JwtAuthGuard) - Uncomment when auth is implemented
  async update(
    @Param('id') id: number,
    @Body() updateOnboardingDto: Partial<CreateOnboardingDto>,
  ): Promise<OnboardingData> {
    return this.onboardingService.update(id, updateOnboardingDto);
  }
}
