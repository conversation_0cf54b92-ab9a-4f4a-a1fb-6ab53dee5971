import e from 'express';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';

export interface UserAuthPayload {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  profileImage: string;
  role: Partial<Role>;
  accessToken: string;
  refreshToken?: string;
  redirectTo?: string;
}

export interface TokenPayloadI {
  sub: number;
  email: string;
  role: string;
  scope: string;
  projectId: string;
}

export interface RequestUserObjectI {
  id: number;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  status: string;
  profileImage?: string;
  role: Partial<Role>;
  redirectTo?: string;
}

export interface AdminRequestI extends Request {
  user: RequestUserObjectI;
}

export interface UserRequestI extends Request {
  user: RequestUserObjectI;
}

// Permission Types
export type PermissionAction =
  | 'canView'
  | 'canCreate'
  | 'canEdit'
  | 'canDelete';

export type PermissionScope =
  | ROLE_SCOPE_ENUM.PLATFORM
  | ROLE_SCOPE_ENUM.COMPANY;

// Permission Interfaces
export interface IPermissionCheck {
  section: string;
  action: string;
  companyId?: number;
}

export interface IUserPermissions {
  userId: number;
  roleId: number;
  roleName: string;
  scope: string;
  companyId?: number;
  permissions: IPermission[];
}

export interface IPermission {
  id: number;
  platformSectionId: number;
  platformSectionName: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

export interface IPermissionDecoratorOptions {
  section: string;
  action: PermissionAction;
  scope: PermissionScope;
}
