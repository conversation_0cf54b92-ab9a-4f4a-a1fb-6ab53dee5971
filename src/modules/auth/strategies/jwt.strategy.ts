import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { TokenPayloadI } from '../auth.interfaces';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      // secretOrKey: configService.get<string>('jwt.secret'),
      secretOrKey: 'my_jwt_secret',
    });
  }

  async validate(payload: TokenPayloadI): Promise<{
    id: number;
    email: string;
    role: string;
    scope: string;
    projectId: string;
  }> {
    return {
      id: payload.sub,
      email: payload.email,
      role: payload.role,
      scope: payload.scope,
      projectId: payload.projectId,
    };
  }
}
