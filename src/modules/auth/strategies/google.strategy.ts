import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../services/auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    const clientId = configService.get<string>('google.clientId');
    const clientSecret = configService.get<string>('google.clientSecret');
    const callbackUrl = configService.get<string>('google.callbackUrl');

    // Use dummy values if Google OAuth is not configured
    const finalClientId = clientId || 'dummy-client-id';
    const finalClientSecret = clientSecret || 'dummy-client-secret';
    const finalCallbackUrl =
      callbackUrl || 'http://localhost:3000/api/auth/google/callback';

    super({
      clientID: finalClientId,
      clientSecret: finalClientSecret,
      callbackURL: finalCallbackUrl,
      scope: ['email', 'profile'],
      // Security best practices
      passReqToCallback: false,
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      // Check if Google OAuth is properly configured
      const clientId = this.configService.get<string>('google.clientId');
      if (!clientId || clientId === 'dummy-client-id') {
        return done(
          new UnauthorizedException(
            'Google OAuth is not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.',
          ),
          null,
        );
      }

      // Validate profile data
      if (!profile || !profile.emails || !profile.emails[0] || !profile.name) {
        return done(
          new UnauthorizedException('Invalid Google profile data received'),
          null,
        );
      }

      // Validate email
      const email = profile.emails[0].value;
      if (!email || !this.isValidEmail(email)) {
        return done(
          new UnauthorizedException(
            'Invalid email address received from Google',
          ),
          null,
        );
      }

      // Extract and validate user data
      const { name, emails, photos } = profile;
      const user = {
        email: email,
        firstName: name.givenName || '',
        lastName: name.familyName || '',
        profileImage: photos && photos[0] ? photos[0].value : null,
        googleId: profile.id,
        googleAccessToken: accessToken,
        googleRefreshToken: refreshToken,
      };

      // Validate required fields
      if (!user.firstName || !user.lastName) {
        return done(
          new UnauthorizedException(
            'Incomplete profile data received from Google',
          ),
          null,
        );
      }

      const result = await this.authService.validateOrCreateGoogleUser(user);
      done(null, result);
    } catch (error) {
      done(error, null);
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
