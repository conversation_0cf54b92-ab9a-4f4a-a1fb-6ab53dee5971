import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from 'src/config/config.interface';
import {
  Google<PERSON>uth<PERSON>rror<PERSON><PERSON><PERSON>,
  GoogleOAuthErrorType,
} from '../utils/google-oauth-errors';

export interface GoogleOAuthConfig {
  clientId: string;
  clientSecret: string;
  callbackUrl: string;
  frontendUrl: string;
}

export interface GoogleTokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}

export interface GoogleUserProfile {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

export interface GoogleUserData {
  email: string;
  firstName: string;
  lastName: string;
  profileImage: string | null;
  googleId: string;
  googleAccessToken: string;
  googleRefreshToken?: string;
}

@Injectable()
export class GoogleOAuthService {
  private readonly config: GoogleOAuthConfig;

  constructor(private readonly configService: ConfigService<AppConfig>) {
    this.config = this.getGoogleConfig();
  }

  /**
   * Get Google OAuth configuration
   */
  private getGoogleConfig(): GoogleOAuthConfig {
    const googleConfig = this.configService.get('google');
    const frontendConfig = this.configService.get('frontendAppUrl');

    const clientId = googleConfig?.clientId || '';
    const clientSecret = googleConfig?.clientSecret || '';
    const callbackUrl =
      googleConfig?.callbackUrl ||
      'http://localhost:3000/api/auth/google/callback';
    const frontendUrl = frontendConfig?.url || 'http://localhost:3100';

    return {
      clientId: clientId || '',
      clientSecret: clientSecret || '',
      callbackUrl,
      frontendUrl,
    };
  }

  /**
   * Check if Google OAuth is properly configured
   */
  isConfigured(): boolean {
    return !!(this.config.clientId && this.config.clientSecret);
  }

  /**
   * Get Google OAuth configuration status
   */
  getConfigStatus() {
    return {
      isConfigured: this.isConfigured(),
      hasClientId: !!this.config.clientId,
      hasClientSecret: !!this.config.clientSecret,
      callbackUrl: this.config.callbackUrl,
      frontendUrl: this.config.frontendUrl,
    };
  }

  /**
   * Generate Google OAuth authorization URL
   */
  generateAuthUrl(): string {
    if (!this.isConfigured()) {
      const error = GoogleOAuthErrorHandler.createError(
        GoogleOAuthErrorType.NOT_CONFIGURED,
      );
      throw new BadRequestException(error.message);
    }

    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.callbackUrl,
      response_type: 'code',
      scope: 'email profile',
      access_type: 'offline',
      prompt: 'consent',
    });

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code: string): Promise<GoogleTokenResponse> {
    if (!this.isConfigured()) {
      const error = GoogleOAuthErrorHandler.createError(
        GoogleOAuthErrorType.NOT_CONFIGURED,
      );
      throw new BadRequestException(error.message);
    }

    const tokenRequestBody = new URLSearchParams({
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
      code: code,
      grant_type: 'authorization_code',
      redirect_uri: this.config.callbackUrl,
    });

    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenRequestBody,
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Google token exchange failed:', errorData);
      const error = GoogleOAuthErrorHandler.createError(
        GoogleOAuthErrorType.TOKEN_EXCHANGE_FAILED,
        'Failed to exchange authorization code for token',
        errorData,
      );
      throw new UnauthorizedException(error.message);
    }

    return await response.json();
  }

  /**
   * Get user profile from Google using access token
   */
  async getUserProfile(accessToken: string): Promise<GoogleUserProfile> {
    const response = await fetch(
      'https://www.googleapis.com/oauth2/v2/userinfo',
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    if (!response.ok) {
      const error = GoogleOAuthErrorHandler.createError(
        GoogleOAuthErrorType.PROFILE_FETCH_FAILED,
        'Failed to fetch user profile from Google',
      );
      throw new UnauthorizedException(error.message);
    }

    return await response.json();
  }

  /**
   * Process Google OAuth callback and return user data
   */
  async processCallback(code: string): Promise<GoogleUserData> {
    // Exchange code for tokens
    const tokenData = await this.exchangeCodeForToken(code);
    const { access_token, refresh_token } = tokenData;

    // Get user profile
    const profile = await this.getUserProfile(access_token);

    // Validate profile data
    if (!profile.email || !profile.verified_email) {
      const error = GoogleOAuthErrorHandler.createError(
        GoogleOAuthErrorType.UNVERIFIED_EMAIL,
      );
      throw new UnauthorizedException(error.message);
    }

    if (!profile.given_name || !profile.family_name) {
      const error = GoogleOAuthErrorHandler.createError(
        GoogleOAuthErrorType.INCOMPLETE_PROFILE,
      );
      throw new UnauthorizedException(error.message);
    }

    // Create user data object
    return {
      email: profile.email,
      firstName: profile.given_name,
      lastName: profile.family_name,
      profileImage: profile.picture || null,
      googleId: profile.id,
      googleAccessToken: access_token,
      googleRefreshToken: refresh_token,
    };
  }

  /**
   * Get frontend URL for redirects
   */
  getFrontendUrl(): string {
    return this.config.frontendUrl;
  }

  /**
   * Generate error redirect URL
   */
  generateErrorRedirectUrl(error: string): string {
    return `${this.config.frontendUrl}/auth/error?error=${encodeURIComponent(error)}`;
  }

  /**
   * Generate success redirect URL
   */
  generateSuccessRedirectUrl(): string {
    return `${this.config.frontendUrl}/auth/success`;
  }
}
