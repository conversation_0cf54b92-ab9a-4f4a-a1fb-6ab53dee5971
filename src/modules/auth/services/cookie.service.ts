import { Injectable } from '@nestjs/common';
import { Response } from 'express';

export interface CookieOptions {
  httpOnly: boolean;
  secure: boolean;
  sameSite: 'strict' | 'lax' | 'none';
  maxAge: number;
  path: string;
}

export interface UserCookieData {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  profileImage: string | null;
  role: {
    id?: number;
    name: string;
    scope: string;
  };
  redirectTo: string;
}

@Injectable()
export class CookieService {
  private readonly isProduction: boolean;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
  }

  /**
   * Get default cookie options
   */
  private getDefaultCookieOptions(): CookieOptions {
    return {
      httpOnly: false,
      secure: this.isProduction,
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/',
    };
  }

  /**
   * Set access token cookie
   */
  setAccessToken(res: Response, token: string): void {
    const options = this.getDefaultCookieOptions();
    res.cookie('access_token_user', token, options);
  }

  /**
   * Set refresh token cookie
   */
  setRefreshToken(res: Response, token: string): void {
    const options = {
      ...this.getDefaultCookieOptions(),
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    };
    res.cookie('refresh_token_user', token, options);
  }

  /**
   * Set user data cookie
   */
  setUserData(res: Response, userData: UserCookieData): void {
    const options = this.getDefaultCookieOptions();
    res.cookie('user', JSON.stringify(userData), options);
  }

  /**
   * Set role cookie
   */
  setRole(res: Response, role: string): void {
    const options = this.getDefaultCookieOptions();
    res.cookie('role', JSON.stringify(role), options);
  }

  /**
   * Set all authentication cookies
   */
  setAuthCookies(
    res: Response,
    accessToken: string,
    refreshToken: string,
    userData: UserCookieData,
    role: string,
  ): void {
    this.setAccessToken(res, accessToken);
    this.setRefreshToken(res, refreshToken);
    this.setUserData(res, userData);
    this.setRole(res, role);
  }

  /**
   * Clear all authentication cookies
   */
  clearAuthCookies(res: Response): void {
    res.clearCookie('access_token_user', { path: '/' });
    res.clearCookie('refresh_token_user', { path: '/' });
    res.clearCookie('user', { path: '/' });
    res.clearCookie('role', { path: '/' });
  }

  /**
   * Clear specific cookie
   */
  clearCookie(res: Response, name: string): void {
    res.clearCookie(name, { path: '/' });
  }
}
