import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../../users/services/users.service';
import { User } from '../../../models/users-models/user.model';
import { AdminLoginDto } from '../dto/admin-login.dto';
import { TokenPayloadI, UserAuthPayload } from '../auth.interfaces';
import * as bcrypt from 'bcrypt';
import { AppConfig } from 'src/config/config.interface';
import { ConfigService } from '@nestjs/config';
import { UserRoleService } from 'src/modules/roles-and-permissions/user-role/user-role.service';
import { LoginDto } from '../dto';
import { SignupDto } from '../dto/signup.dto';
import { RolesService } from 'src/modules/roles-and-permissions/roles/roles.service';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';
import { ROLES_ENUM } from 'src/modules/roles-and-permissions/roles/utils/enums';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { Sequelize } from 'sequelize-typescript';
import { EmailService } from '../../email/email.service';
import { VerifyEmailDto } from '../dto/verify-email.dto';
import { ResendOtpDto } from '../dto/resend-otp.dto';
import { Transaction } from 'sequelize';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly userRoleService: UserRoleService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService<AppConfig>,
    private readonly rolesService: RolesService,
    private readonly sequelize: Sequelize,
    private readonly emailService: EmailService,
  ) {}

  async adminLogin(loginDto: AdminLoginDto): Promise<UserAuthPayload> {
    const { email, password } = loginDto;

    const user = await this.usersService.findByEmailWithPassword(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await this.usersService.validatePassword(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userRole = await this.userRoleService.getRoleForUser(user.id);

    const projectId =
      this.configService.get<AppConfig['fileStorage']>('fileStorage').projectId;

    const tokens = await this.generateTokens({
      sub: user.id,
      email: user.email,
      role: userRole.role.name,
      scope: userRole.role.scope,
      projectId: projectId,
    });

    await this.usersService.storeUserRefreshToken(user.id, tokens.refreshToken);

    const userData: UserAuthPayload = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        scope: userRole.role.scope,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };

    return userData;
  }

  async invalidateToken(token: string): Promise<void> {
    const decoded = this.jwtService.decode(token) as TokenPayloadI;

    if (!decoded?.sub) return;

    const user = await this.usersService.findOneWithRefreshToken(decoded.sub);
    if (!user || !user.refreshToken) return;

    if (token !== user.refreshToken) return;

    await this.usersService.updateUser(user.id, { refreshToken: null });
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmailWithPassword(email);

    if (!user) return null;

    const isValid = await this.usersService.validatePassword(
      password,
      user.password,
    );

    if (!isValid) return null;

    const { password: _, ...result } = user;
    return result;
  }

  async userSignup(signupDto: SignupDto): Promise<UserAuthPayload> {
    const { username, email, password, firstName, lastName } = signupDto;

    // Start transaction
    const transaction = await this.sequelize.transaction();

    try {
      // Create user with initial setup
      const { user, role } = await this.createUserWithInitialSetup(
        {
          username,
          email,
          password,
          firstName,
          lastName,
          redirectTo: RedirectSectionEnum.OTP,
        },
        transaction,
      );

      const projectId =
        this.configService.get<AppConfig['fileStorage']>(
          'fileStorage',
        ).projectId;

      // Generate OTP
      const otp = this.generateOtp();
      const hashedOtp = await this.hashOtp(otp);
      const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Update user with OTP fields
      await this.usersService.updateUser(
        user.id,
        {
          otp: hashedOtp,
          otpExpiry,
          isEmailVerified: false,
        },
        transaction,
      );

      // Send OTP to email using EmailService
      await this.emailService.sendOtpEmail(email, otp);

      // Generate tokens
      const tokens = await this.generateTokens({
        sub: user.id,
        email: user.email,
        role: role.name,
        scope: role.scope,
        projectId: projectId,
      });

      // Store refresh token
      await this.usersService.storeUserRefreshToken(
        user.id,
        tokens.refreshToken,
        transaction,
      );

      // Commit transaction
      await transaction.commit();

      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        profileImage: user.profileImage,
        role: {
          id: role.id,
          name: role.name,
          scope: role.scope,
        },
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        redirectTo: user.redirectTo,
      };
    } catch (error) {
      // Rollback transaction on any error
      await transaction.rollback();
      console.log(
        'here is *',
        'Transaction rolled back due to error:',
        error.message,
      );
      throw error;
    }
  }

  /**
   * Create user with initial role and permissions setup
   */
  private async createUserWithInitialSetup(
    userData: {
      username: string;
      email: string;
      password: string;
      firstName: string;
      lastName: string;
      redirectTo: string;
    },
    transaction: Transaction,
  ): Promise<{ user: any; role: any }> {
    // Create new role (Company Super Admin)
    const companySuperAdminRole =
      await this.rolesService.createRoleWithoutCompanyId(
        {
          name: ROLES_ENUM.COMPANY_SUPER_ADMIN,
          scope: ROLE_SCOPE_ENUM.COMPANY,
        },
        transaction,
      );

    // Give all company-level permissions to the role
    await this.rolesService.giveCompanySuperAdminPermissionsByRoleId(
      companySuperAdminRole.id,
      transaction,
    );

    // Create user with role: company super admin
    const user = await this.usersService.createUserWithTransaction(
      {
        ...userData,
        roleId: companySuperAdminRole.id,
      },
      undefined,
      transaction,
    );

    // Assign role to user
    await this.userRoleService.assignRoleWithTransaction(
      user.id,
      companySuperAdminRole.id,
      transaction,
    );

    return { user, role: companySuperAdminRole };
  }

  // Helper: Generate 6-digit OTP
  generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Helper: Hash OTP
  async hashOtp(otp: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(otp, salt);
  }

  async userLogin(loginDto: LoginDto): Promise<UserAuthPayload> {
    const { email, password } = loginDto;

    const user = await this.usersService.findByEmailWithPassword(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await this.usersService.validatePassword(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userRole = await this.userRoleService.getRoleForUser(user.id);
    const projectId =
      this.configService.get<AppConfig['fileStorage']>('fileStorage').projectId;

    const payload: TokenPayloadI = {
      sub: user.id,
      email: user.email,
      role: userRole.role.name,
      scope: userRole.role.scope,
      projectId: projectId,
    };

    const tokens = await this.generateTokens(payload);

    await this.usersService.storeUserRefreshToken(user.id, tokens.refreshToken);

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        scope: userRole.role.scope,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      redirectTo: user.redirectTo,
    };
  }

  async getUserProfile(userId: number): Promise<Partial<User>> {
    const user = await this.usersService.findOne(userId);
    return user;
  }

  async generateTokens(payload: TokenPayloadI) {
    const jwtConfig = this.configService.get<AppConfig['jwt']>('jwt');

    const accessToken = this.jwtService.sign(payload, {
      secret: jwtConfig.secret,
      expiresIn: jwtConfig.expiresIn,
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: jwtConfig.refreshSecret,
      expiresIn: jwtConfig.refreshExpiresIn,
    });

    return { accessToken, refreshToken };
  }

  async refreshTokens(refreshToken: string): Promise<UserAuthPayload> {
    const jwtConfig = this.configService.get<AppConfig['jwt']>('jwt');

    const decoded = this.jwtService.verify(refreshToken, {
      secret: jwtConfig.refreshSecret,
    });

    const user = await this.usersService.findOneWithRefreshToken(decoded.sub);

    if (!user || !user.refreshToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    if (refreshToken !== user.refreshToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const userRole = await this.userRoleService.getRoleForUser(user.id);

    const role = userRole.role.name;

    const projectId =
      this.configService.get<AppConfig['fileStorage']>('fileStorage').projectId;

    const payload: TokenPayloadI = {
      sub: user.id,
      email: user.email,
      role: role,
      scope: userRole.role.scope,
      projectId: projectId,
    };

    const tokens = await this.generateTokens(payload);

    await this.usersService.updateUser(user.id, {
      refreshToken: tokens.refreshToken,
    });

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        scope: userRole.role.scope,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };
  }

  async getCurrentAdmin(adminId: number): Promise<Partial<User>> {
    const admin = await this.usersService.findOne(adminId);
    if (!admin) {
      throw new UnauthorizedException('Admin not found');
    }
    return admin;
  }

  // --- OTP Verification and Resend ---
  public async verifyEmailOtp(
    dto: VerifyEmailDto,
  ): Promise<{ message: string }> {
    const { email, otp } = dto;
    const user = await this.usersService.findByEmail(email);
    if (!user) throw new NotFoundException('User not found');
    if (user.isEmailVerified)
      throw new BadRequestException('Email already verified');
    if (!user.otp || !user.otpExpiry)
      throw new BadRequestException('No OTP set for this user');
    if (user.otpExpiry < new Date())
      throw new ForbiddenException('OTP expired');
    const isMatch = await bcrypt.compare(otp, user.otp);
    if (!isMatch) throw new BadRequestException('Invalid OTP');
    await this.usersService.updateUser(user.id, {
      isEmailVerified: true,
      otp: null,
      otpExpiry: null,
      redirectTo: RedirectSectionEnum.CREATE_COMPANY,
    });
    return { message: 'Email verified successfully' };
  }

  public async resendOtp(dto: ResendOtpDto): Promise<{ message: string }> {
    const { email } = dto;
    const user = await this.usersService.findByEmail(email);
    if (!user) throw new NotFoundException('User not found');
    if (user.isEmailVerified)
      throw new BadRequestException('Email already verified');
    // Optionally: Add rate limiting logic here
    const otp = this.generateOtp();
    const hashedOtp = await this.hashOtp(otp);
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    await this.usersService.updateUser(user.id, {
      otp: hashedOtp,
      otpExpiry,
    });
    await this.emailService.sendOtpEmail(email, otp);
    return { message: 'OTP resent successfully' };
  }

  // --- Google OAuth Methods ---
  async validateOrCreateGoogleUser(
    googleUserData: any,
  ): Promise<UserAuthPayload> {
    const {
      email,
      firstName,
      lastName,
      profileImage,
      googleId,
      googleAccessToken,
      googleRefreshToken,
    } = googleUserData;

    // Validate input data
    if (!email || !firstName || !lastName || !googleId) {
      throw new UnauthorizedException('Invalid Google user data provided');
    }

    // Check if user exists by Google ID
    let user = await this.usersService.findByGoogleId(googleId);

    if (!user) {
      // Check if user exists by email
      user = await this.usersService.findByEmail(email);

      if (user) {
        // User exists but doesn't have Google ID - link the account
        await this.usersService.updateUser(user.id, {
          googleId,
          googleAccessToken,
          googleRefreshToken,
          googleTokenExpiry: new Date(Date.now() + 3600 * 1000), // 1 hour
          isEmailVerified: true, // Google accounts are pre-verified
        });
      } else {
        // Create new user with Google OAuth data
        const transaction = await this.sequelize.transaction();

        try {
          // Generate a unique username
          const baseUsername = `${firstName.toLowerCase()}${lastName.toLowerCase()}`;
          let username = baseUsername;
          let counter = 1;

          while (await this.usersService.findByUsername(username)) {
            username = `${baseUsername}${counter}`;
            counter++;
          }

          // Get default password from config
          const defaultPassword =
            this.configService.get<AppConfig['user']>('user').defaultPassword;

          // Create user with initial setup using reusable function
          const { user: newUser, role: companySuperAdminRole } =
            await this.createUserWithInitialSetup(
              {
                username,
                email,
                password: defaultPassword,
                firstName,
                lastName,
                redirectTo: RedirectSectionEnum.CREATE_COMPANY,
              },
              transaction,
            );

          // Update user with Google-specific data
          await this.usersService.updateUser(
            newUser.id,
            {
              profileImage,
              googleId,
              googleAccessToken,
              googleRefreshToken,
              googleTokenExpiry: new Date(Date.now() + 3600 * 1000), // 1 hour
              isEmailVerified: true, // Google accounts are pre-verified
            },
            transaction,
          );

          await transaction.commit();
          user = newUser;
        } catch (error) {
          await transaction.rollback();
          console.log('Transaction rolled back due to error:', error.message);
          throw error;
        }
      }
    } else {
      // Update existing Google user's tokens
      await this.usersService.updateUser(user.id, {
        googleAccessToken,
        googleRefreshToken,
        googleTokenExpiry: new Date(Date.now() + 3600 * 1000), // 1 hour
      });
    }

    // Get user role
    const userRole = await this.userRoleService.getRoleForUser(user.id);

    const projectId =
      this.configService.get<AppConfig['fileStorage']>('fileStorage').projectId;
    // Generate JWT tokens
    const payload: TokenPayloadI = {
      sub: user.id,
      email: user.email,
      role: userRole.role.name,
      scope: userRole.role.scope,
      projectId,
    };

    const tokens = await this.generateTokens(payload);

    // Store refresh token
    await this.usersService.storeUserRefreshToken(user.id, tokens.refreshToken);

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        scope: userRole.role.scope,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      redirectTo: user.redirectTo,
    };
  }

  async googleLogin(googleUserData: any): Promise<UserAuthPayload> {
    return this.validateOrCreateGoogleUser(googleUserData);
  }

  // Additional security method for token refresh
  async refreshGoogleTokens(
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const user = await this.usersService.findOne(userId);

      if (!user || !user.googleId) {
        return {
          success: false,
          message: 'User not found or not linked to Google',
        };
      }

      // Here you would implement Google token refresh logic
      // This is a placeholder for future implementation

      return { success: true, message: 'Google tokens refreshed successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to refresh Google tokens' };
    }
  }
}
