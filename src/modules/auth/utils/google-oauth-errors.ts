export enum GoogleOAuthErrorType {
  NOT_CONFIGURED = 'oauth_not_configured',
  NO_AUTHORIZATION_CODE = 'no_authorization_code',
  TOKEN_EXCHANGE_FAILED = 'token_exchange_failed',
  PROFILE_FETCH_FAILED = 'profile_fetch_failed',
  INVALID_PROFILE_DATA = 'invalid_profile_data',
  UNVERIFIED_EMAIL = 'unverified_email',
  INCOMPLETE_PROFILE = 'incomplete_profile',
  USER_CREATION_FAILED = 'user_creation_failed',
  UNKNOWN_ERROR = 'unknown_error',
}

export interface GoogleOAuthError {
  type: GoogleOAuthErrorType;
  message: string;
  details?: string;
}

export class GoogleOAuthErrorHandler {
  /**
   * Create a standardized error object
   */
  static createError(
    type: GoogleOAuthErrorType,
    message?: string,
    details?: string,
  ): GoogleOAuthError {
    const defaultMessages = {
      [GoogleOAuthErrorType.NOT_CONFIGURED]: 'Google OAuth is not configured',
      [GoogleOAuthErrorType.NO_AUTHORIZATION_CODE]:
        'No authorization code received',
      [GoogleOAuthErrorType.TOKEN_EXCHANGE_FAILED]:
        'Failed to exchange authorization code for token',
      [GoogleOAuthErrorType.PROFILE_FETCH_FAILED]:
        'Failed to fetch user profile from Google',
      [GoogleOAuthErrorType.INVALID_PROFILE_DATA]:
        'Invalid profile data received from Google',
      [GoogleOAuthErrorType.UNVERIFIED_EMAIL]: 'Email address is not verified',
      [GoogleOAuthErrorType.INCOMPLETE_PROFILE]:
        'Incomplete profile data received from Google',
      [GoogleOAuthErrorType.USER_CREATION_FAILED]:
        'Failed to create or update user',
      [GoogleOAuthErrorType.UNKNOWN_ERROR]: 'An unknown error occurred',
    };

    return {
      type,
      message: message || defaultMessages[type],
      details,
    };
  }

  /**
   * Handle specific error types and return appropriate error messages
   */
  static handleError(error: any): GoogleOAuthError {
    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('not configured')) {
        return this.createError(GoogleOAuthErrorType.NOT_CONFIGURED);
      }

      if (error.message.includes('authorization code')) {
        return this.createError(GoogleOAuthErrorType.NO_AUTHORIZATION_CODE);
      }

      if (error.message.includes('token exchange')) {
        return this.createError(
          GoogleOAuthErrorType.TOKEN_EXCHANGE_FAILED,
          error.message,
        );
      }

      if (error.message.includes('user profile')) {
        return this.createError(
          GoogleOAuthErrorType.PROFILE_FETCH_FAILED,
          error.message,
        );
      }

      if (error.message.includes('unverified email')) {
        return this.createError(GoogleOAuthErrorType.UNVERIFIED_EMAIL);
      }

      if (error.message.includes('incomplete profile')) {
        return this.createError(GoogleOAuthErrorType.INCOMPLETE_PROFILE);
      }

      if (error.message.includes('user creation')) {
        return this.createError(
          GoogleOAuthErrorType.USER_CREATION_FAILED,
          error.message,
        );
      }
    }

    // Default to unknown error
    return this.createError(
      GoogleOAuthErrorType.UNKNOWN_ERROR,
      error.message || 'An unexpected error occurred',
    );
  }

  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(errorType: GoogleOAuthErrorType): string {
    const messages = {
      [GoogleOAuthErrorType.NOT_CONFIGURED]:
        'Google OAuth is not configured. Please contact support.',
      [GoogleOAuthErrorType.NO_AUTHORIZATION_CODE]:
        'Authorization failed. Please try again.',
      [GoogleOAuthErrorType.TOKEN_EXCHANGE_FAILED]:
        'Authentication failed. Please try again.',
      [GoogleOAuthErrorType.PROFILE_FETCH_FAILED]:
        'Failed to get your profile. Please try again.',
      [GoogleOAuthErrorType.INVALID_PROFILE_DATA]:
        'Invalid profile data. Please try again.',
      [GoogleOAuthErrorType.UNVERIFIED_EMAIL]:
        'Please use a verified email address.',
      [GoogleOAuthErrorType.INCOMPLETE_PROFILE]:
        'Incomplete profile data. Please try again.',
      [GoogleOAuthErrorType.USER_CREATION_FAILED]:
        'Account creation failed. Please try again.',
      [GoogleOAuthErrorType.UNKNOWN_ERROR]:
        'Something went wrong. Please try again.',
    };

    return messages[errorType] || messages[GoogleOAuthErrorType.UNKNOWN_ERROR];
  }
}
