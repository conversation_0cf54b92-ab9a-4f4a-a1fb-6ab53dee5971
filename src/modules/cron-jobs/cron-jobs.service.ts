import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/sequelize';
import { TimeAttendance } from '../../models/time-attendance.model';
import { User } from '../../models/users-models/user.model';
import { Op } from 'sequelize';
import { ConfigService } from '@nestjs/config';
import { CronJobResult } from './interfaces/cron-job.interface';
import { CronJobExecutionInterceptor } from './interceptors/cron-job-execution.interceptor';
import {
  CronJob,
  WithLock,
  WithMonitoring,
  HighPriority,
  Critical,
  WithTimeout,
  WithRetry,
  ResourceIntensive,
} from './decorators/cron-job.decorator';
import { EmployeeTimeAttendanceService } from '../time-attendance/services/time-attendance.service';

@Injectable()
export class CronJobsService {
  private readonly logger = new Logger(CronJobsService.name);

  constructor(
    private readonly employeeTimeAttendanceService: EmployeeTimeAttendanceService,
    @InjectModel(User)
    private readonly userModel: typeof User,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly configService: ConfigService,
    private readonly executionInterceptor: CronJobExecutionInterceptor,
  ) {}

  /**
   * Daily attendance reminder - runs every day at 9:00 AM
   * Sends reminders to employees who haven't clocked in
   */
  @Cron(CronExpression.EVERY_DAY_AT_9AM, {
    name: 'daily-attendance-reminder',
    timeZone: 'UTC',
  })
  async handleDailyAttendanceReminder(): Promise<CronJobResult> {
    try {
      this.logger.log('Starting daily attendance reminder job');

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Find users who haven't clocked in today
      const usersWithoutAttendance = await this.userModel.findAll({
        where: {
          isActive: true,
          id: {
            [Op.notIn]:
              await this.employeeTimeAttendanceService.findAllUserIdsForDate(
                today,
              ),
          },
        },
        attributes: ['id', 'email', 'firstName', 'lastName'],
      });

      this.logger.log(
        `Found ${usersWithoutAttendance.length} users without attendance today`,
      );

      // TODO: Send email notifications to users
      // await this.emailService.sendAttendanceReminder(usersWithoutAttendance);

      return {
        success: true,
        message: `Attendance reminder sent to ${usersWithoutAttendance.length} users`,
        processedCount: usersWithoutAttendance.length,
      };
    } catch (error) {
      this.logger.error('Error in daily attendance reminder job', error.stack);
      return {
        success: false,
        message: 'Failed to process daily attendance reminder',
        error: error.message,
      };
    }
  }

  /**
   * Auto clock-out job - runs every day at midnight
   * Automatically clocks out users who have been clocked in for more than 12 hours
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    name: 'auto-clock-out',
    timeZone: 'UTC',
  })
  async handleAutoClockOut(): Promise<CronJobResult> {
    try {
      this.logger.log('Starting auto clock-out job');

      const twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000);

      // Find attendance records that need auto clock-out
      const attendanceToUpdate =
        await this.employeeTimeAttendanceService.findAllAttendanceRecordsToAutoClockOut(
          twelveHoursAgo,
        );

      let updatedCount = 0;
      for (const attendance of attendanceToUpdate) {
        await attendance.update({
          clockOutTime: new Date(),
          notes: attendance.notes
            ? `${attendance.notes} (Auto clock-out after 12 hours)`
            : 'Auto clock-out after 12 hours',
        });
        updatedCount++;
      }

      this.logger.log(`Auto clock-out completed for ${updatedCount} records`);

      return {
        success: true,
        message: `Auto clock-out completed for ${updatedCount} records`,
        processedCount: updatedCount,
      };
    } catch (error) {
      this.logger.error('Error in auto clock-out job', error.stack);
      return {
        success: false,
        message: 'Failed to process auto clock-out',
        error: error.message,
      };
    }
  }

  /**
   * Weekly attendance report - runs every Monday at 8:00 AM
   * Generates and sends weekly attendance reports to managers
   */
  @Cron('0 8 * * 1', {
    name: 'weekly-attendance-report',
    timeZone: 'UTC',
  })
  async handleWeeklyAttendanceReport(): Promise<CronJobResult> {
    try {
      this.logger.log('Starting weekly attendance report job');

      const lastWeekStart = new Date();
      lastWeekStart.setDate(lastWeekStart.getDate() - 7);
      lastWeekStart.setHours(0, 0, 0, 0);

      const lastWeekEnd = new Date();
      lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
      lastWeekEnd.setHours(23, 59, 59, 999);

      // Get attendance data for last week
      const weeklyAttendance =
        await this.employeeTimeAttendanceService.findAllAttendanceRecordsForDateRange(
          lastWeekStart,
          lastWeekEnd,
        );

      this.logger.log(
        `Generated weekly report with ${weeklyAttendance.length} attendance records`,
      );

      // TODO: Generate and send weekly report
      // await this.emailService.sendWeeklyAttendanceReport(weeklyAttendance);

      return {
        success: true,
        message: `Weekly attendance report generated with ${weeklyAttendance.length} records`,
        processedCount: weeklyAttendance.length,
      };
    } catch (error) {
      this.logger.error('Error in weekly attendance report job', error.stack);
      return {
        success: false,
        message: 'Failed to generate weekly attendance report',
        error: error.message,
      };
    }
  }

  /**
   * Monthly payroll processing - runs on the 1st of every month at 2:00 AM
   * Processes payroll for the previous month
   */
  @Cron('0 2 1 * *', {
    name: 'monthly-payroll-processing',
    timeZone: 'UTC',
  })
  async handleMonthlyPayrollProcessing(): Promise<CronJobResult> {
    try {
      this.logger.log('Starting monthly payroll processing job');

      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      // TODO: Implement payroll processing logic
      // const payrollResults = await this.payrollService.processMonthlyPayroll(lastMonth);

      this.logger.log('Monthly payroll processing completed');

      return {
        success: true,
        message: 'Monthly payroll processing completed successfully',
      };
    } catch (error) {
      this.logger.error('Error in monthly payroll processing job', error.stack);
      return {
        success: false,
        message: 'Failed to process monthly payroll',
        error: error.message,
      };
    }
  }

  /**
   * Database cleanup job - runs every Sunday at 3:00 AM
   * Cleans up old records and maintains database performance
   */
  @Cron('0 3 * * 0', {
    name: 'database-cleanup',
    timeZone: 'UTC',
  })
  async handleDatabaseCleanup(): Promise<CronJobResult> {
    try {
      this.logger.log('Starting database cleanup job');

      // Clean up attendance records older than 2 years
      const twoYearsAgo = new Date();
      twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);

      const deletedCount =
        await this.employeeTimeAttendanceService.deleteAttendanceRecordsOlderThan(
          twoYearsAgo,
        );

      this.logger.log(`Cleaned up ${deletedCount} old attendance records`);

      return {
        success: true,
        message: `Database cleanup completed. Deleted ${deletedCount} old records`,
        processedCount: deletedCount,
      };
    } catch (error) {
      this.logger.error('Error in database cleanup job', error.stack);
      return {
        success: false,
        message: 'Failed to perform database cleanup',
        error: error.message,
      };
    }
  }

  /**
   * Health check job - runs every 5 minutes
   * Monitors system health and logs status
   */
  @Cron('*/5 * * * *', {
    name: 'health-check',
    timeZone: 'UTC',
  })
  async handleHealthCheck(): Promise<CronJobResult> {
    try {
      this.logger.debug('Performing health check');

      // Check database connectivity
      const userCount = await this.userModel.count();

      // Check if any jobs are running
      const jobs = this.schedulerRegistry.getCronJobs();

      this.logger.debug(
        `Health check passed. Users: ${userCount}, Active jobs: ${jobs.size}`,
      );

      return {
        success: true,
        message: 'Health check completed successfully',
      };
    } catch (error) {
      this.logger.error('Health check failed', error.stack);
      return {
        success: false,
        message: 'Health check failed',
        error: error.message,
      };
    }
  }

  /**
   * Get all registered cron jobs
   */
  getAllJobs(): Map<string, any> {
    return this.schedulerRegistry.getCronJobs();
  }

  /**
   * Stop a specific cron job
   */
  stopJob(jobName: string): boolean {
    try {
      const job = this.schedulerRegistry.getCronJob(jobName);
      job.stop();
      this.logger.log(`Stopped cron job: ${jobName}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to stop job ${jobName}`, error.stack);
      return false;
    }
  }

  /**
   * Start a specific cron job
   */
  startJob(jobName: string): boolean {
    try {
      const job = this.schedulerRegistry.getCronJob(jobName);
      job.start();
      this.logger.log(`Started cron job: ${jobName}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to start job ${jobName}`, error.stack);
      return false;
    }
  }

  /**
   * Get job status
   */
  getJobStatus(jobName: string): { running: boolean; lastExecution?: Date } {
    try {
      const job = this.schedulerRegistry.getCronJob(jobName);
      return {
        running: (job as any).running,
        lastExecution: (job as any).lastExecution,
      };
    } catch (error) {
      this.logger.error(`Failed to get status for job ${jobName}`, error.stack);
      return { running: false };
    }
  }
}
