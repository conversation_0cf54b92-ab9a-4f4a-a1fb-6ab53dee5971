import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CronJobsService } from './cron-jobs.service';
import { <PERSON>ronJobsController } from './cron-jobs.controller';
import { CronJobsUtilService } from './cron-jobs.util.service';
import { CronJobMonitoringService } from './services/cron-job-monitoring.service';
import { CronJobLockService } from './services/cron-job-lock.service';
import { CronJobExecutionInterceptor } from './interceptors/cron-job-execution.interceptor';
import { TimeAttendanceModule } from '../time-attendance/time-attendance.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { EmailModule } from '../email/email.module';
import { PayrollsModule } from '../payrolls/payrolls.module';
import { LeaveRequestsModule } from '../leave-requests/leave-requests.module';
import { User } from '../../models/users-models/user.model';
import { RedisCacheService } from 'src/common/services/redis-cache.service';

@Module({
  imports: [
    SequelizeModule.forFeature([User]),
    EventEmitterModule.forRoot(),
    TimeAttendanceModule,
    NotificationsModule,
    EmailModule,
    PayrollsModule,
    LeaveRequestsModule,
  ],
  controllers: [CronJobsController],
  providers: [
    CronJobsService,
    CronJobsUtilService,
    CronJobMonitoringService,
    CronJobLockService,
    CronJobExecutionInterceptor,
    RedisCacheService,
  ],
  exports: [
    CronJobsService,
    CronJobsUtilService,
    CronJobMonitoringService,
    CronJobLockService,
  ],
})
export class CronJobsModule {}
