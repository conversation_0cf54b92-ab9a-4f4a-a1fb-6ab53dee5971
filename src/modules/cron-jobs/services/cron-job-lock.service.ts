import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from '../../../common/services/redis-cache.service';
import { CronJobLock } from '../interfaces/cron-job.interface';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class CronJobLockService {
  private readonly logger = new Logger(CronJobLockService.name);
  private readonly lockPrefix = 'cron:lock:';
  private readonly instanceId = uuidv4();

  constructor(
    private readonly configService: ConfigService,
    private readonly redisCacheService: RedisCacheService,
  ) {}

  /**
   * Acquire a distributed lock for a job
   */
  async acquireLock(
    jobName: string,
    lockDuration: number = 300000,
  ): Promise<string | null> {
    try {
      const lockKey = `${this.lockPrefix}${jobName}`;
      const lockId = uuidv4();
      const now = new Date();
      const expiresAt = new Date(now.getTime() + lockDuration);

      const lock: CronJobLock = {
        jobName,
        lockId,
        acquiredAt: now,
        expiresAt,
        instanceId: this.instanceId,
      };

      // Try to acquire lock using Redis SET with NX (only if not exists)
      const acquired = await this.redisCacheService.set(
        lockKey,
        lock,
        Math.ceil(lockDuration / 1000), // Convert to seconds
        'NX', // Only set if key doesn't exist
      );

      if (acquired) {
        this.logger.debug(`Acquired lock for job ${jobName} (${lockId})`);
        return lockId;
      } else {
        // Check if existing lock is expired
        const existingLock =
          await this.redisCacheService.get<CronJobLock>(lockKey);
        if (existingLock && existingLock.expiresAt < now) {
          // Force acquire expired lock
          await this.redisCacheService.del(lockKey);
          const forceAcquired = await this.redisCacheService.set(
            lockKey,
            lock,
            Math.ceil(lockDuration / 1000),
            'NX',
          );

          if (forceAcquired) {
            this.logger.debug(
              `Force acquired expired lock for job ${jobName} (${lockId})`,
            );
            return lockId;
          }
        }

        this.logger.debug(
          `Failed to acquire lock for job ${jobName} - already locked`,
        );
        return null;
      }
    } catch (error) {
      this.logger.error(
        `Failed to acquire lock for job ${jobName}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Release a distributed lock
   */
  async releaseLock(jobName: string, lockId: string): Promise<boolean> {
    try {
      const lockKey = `${this.lockPrefix}${jobName}`;
      const existingLock = await this.redisCacheService.get(lockKey);

      if (!existingLock) {
        this.logger.debug(`Lock for job ${jobName} not found`);
        return true;
      }

      // Only release if we own the lock
      if (
        (existingLock as CronJobLock).lockId === lockId &&
        (existingLock as CronJobLock).instanceId === this.instanceId
      ) {
        await this.redisCacheService.del(lockKey);
        this.logger.debug(`Released lock for job ${jobName} (${lockId})`);
        return true;
      } else {
        this.logger.warn(
          `Attempted to release lock for job ${jobName} but don't own it`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Failed to release lock for job ${jobName}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Check if a job is currently locked
   */
  async isJobLocked(jobName: string): Promise<boolean> {
    try {
      const lockKey = `${this.lockPrefix}${jobName}`;
      const lock = await this.redisCacheService.get(lockKey);

      if (!lock) {
        return false;
      }

      // Check if lock is expired
      if ((lock as CronJobLock).expiresAt < new Date()) {
        await this.redisCacheService.del(lockKey);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to check lock status for job ${jobName}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Get lock information for a job
   */
  async getLockInfo(jobName: string): Promise<CronJobLock | null> {
    try {
      const lockKey = `${this.lockPrefix}${jobName}`;
      const lock = await this.redisCacheService.get(lockKey);

      if (!lock) {
        return null;
      }

      // Check if lock is expired
      if ((lock as CronJobLock).expiresAt < new Date()) {
        await this.redisCacheService.del(lockKey);
        return null;
      }

      return lock as CronJobLock;
    } catch (error) {
      this.logger.error(
        `Failed to get lock info for job ${jobName}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Get all active locks
   */
  async getAllLocks(): Promise<CronJobLock[]> {
    try {
      const keys = await this.redisCacheService.keys(`${this.lockPrefix}*`);
      const locks: CronJobLock[] = [];
      const now = new Date();

      for (const key of keys) {
        const lock = await this.redisCacheService.get<CronJobLock>(key);
        if (lock && (lock as CronJobLock).expiresAt > now) {
          locks.push(lock as CronJobLock);
        } else if (lock && (lock as CronJobLock).expiresAt <= now) {
          // Clean up expired lock
          await this.redisCacheService.del(key);
        }
      }

      return locks;
    } catch (error) {
      this.logger.error('Failed to get all locks', error.stack);
      return [];
    }
  }

  /**
   * Force release a lock (admin function)
   */
  async forceReleaseLock(jobName: string): Promise<boolean> {
    try {
      const lockKey = `${this.lockPrefix}${jobName}`;
      const deleted = await this.redisCacheService.del(lockKey);

      if (deleted) {
        this.logger.warn(`Force released lock for job ${jobName}`);
        return true;
      } else {
        this.logger.debug(`No lock found to force release for job ${jobName}`);
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Failed to force release lock for job ${jobName}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Extend lock duration
   */
  async extendLock(
    jobName: string,
    lockId: string,
    additionalDuration: number = 300000,
  ): Promise<boolean> {
    try {
      const lockKey = `${this.lockPrefix}${jobName}`;
      const existingLock = await this.redisCacheService.get(lockKey);

      if (!existingLock) {
        return false;
      }

      // Only extend if we own the lock
      if (
        (existingLock as CronJobLock).lockId === lockId &&
        (existingLock as CronJobLock).instanceId === this.instanceId
      ) {
        const newExpiresAt = new Date(
          (existingLock as CronJobLock).expiresAt.getTime() +
            additionalDuration,
        );
        const updatedLock: CronJobLock = {
          ...(existingLock as CronJobLock),
          expiresAt: newExpiresAt,
        };

        await this.redisCacheService.set(
          lockKey,
          updatedLock,
          Math.ceil((newExpiresAt.getTime() - Date.now()) / 1000),
        );

        this.logger.debug(`Extended lock for job ${jobName} (${lockId})`);
        return true;
      } else {
        this.logger.warn(
          `Attempted to extend lock for job ${jobName} but don't own it`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Failed to extend lock for job ${jobName}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Clean up expired locks
   */
  async cleanupExpiredLocks(): Promise<number> {
    try {
      const keys = await this.redisCacheService.keys(`${this.lockPrefix}*`);
      let cleanedCount = 0;
      const now = new Date();

      for (const key of keys) {
        const lock = await this.redisCacheService.get<CronJobLock>(key);
        if (lock && (lock as CronJobLock).expiresAt <= now) {
          await this.redisCacheService.del(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`Cleaned up ${cleanedCount} expired locks`);
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired locks', error.stack);
      return 0;
    }
  }

  /**
   * Get instance ID
   */
  getInstanceId(): string {
    return this.instanceId;
  }
}
