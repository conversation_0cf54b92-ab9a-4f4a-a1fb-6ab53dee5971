import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  CronJobStats,
  CronJobExecution,
  CronJobHealth,
  CronJobMetrics,
  CronJobAlert,
} from '../interfaces/cron-job.interface';
import { RedisCacheService } from '../../../common/services/redis-cache.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class CronJobMonitoringService {
  private readonly logger = new Logger(CronJobMonitoringService.name);
  private readonly statsPrefix = 'cron:stats:';
  private readonly executionPrefix = 'cron:execution:';
  private readonly alertPrefix = 'cron:alert:';
  private readonly healthPrefix = 'cron:health:';

  constructor(
    private readonly configService: ConfigService,
    private readonly redisCacheService: RedisCacheService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Record job execution metrics
   */
  async recordExecution(execution: CronJobExecution): Promise<void> {
    try {
      const jobId = execution.jobId;
      const jobName = execution.jobName;

      // Store execution details
      await this.redisCacheService.set(
        `${this.executionPrefix}${jobId}`,
        execution,
        86400, // 24 hours
      );

      // Update job statistics
      await this.updateJobStats(jobName, execution);

      // Emit execution event
      this.eventEmitter.emit('cron.job.executed', execution);

      // Check for alerts
      await this.checkForAlerts(jobName, execution);

      this.logger.debug(`Recorded execution for job ${jobName} (${jobId})`);
    } catch (error) {
      this.logger.error('Failed to record job execution', error.stack);
    }
  }

  /**
   * Update job statistics
   */
  private async updateJobStats(
    jobName: string,
    execution: CronJobExecution,
  ): Promise<void> {
    const statsKey = `${this.statsPrefix}${jobName}`;
    const stats: CronJobStats = (await this.redisCacheService.get<CronJobStats>(
      statsKey,
    )) || {
      jobName,
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      lastExecution: null,
      lastSuccess: null,
      lastFailure: null,
      nextExecution: null,
      isRunning: false,
    };

    // Update basic stats
    stats.totalExecutions++;
    stats.lastExecution = execution.startTime;

    if (execution.success) {
      stats.successfulExecutions++;
      stats.lastSuccess = execution.endTime || new Date();
    } else {
      stats.failedExecutions++;
      stats.lastFailure = execution.endTime || new Date();
    }

    // Update average execution time
    if (execution.duration) {
      const totalTime =
        stats.averageExecutionTime * (stats.totalExecutions - 1) +
        execution.duration;
      stats.averageExecutionTime = totalTime / stats.totalExecutions;
    }

    // Store updated stats
    await this.redisCacheService.set(statsKey, stats, 0); // No expiration
  }

  /**
   * Get job statistics
   */
  async getJobStats(jobName: string): Promise<CronJobStats | null> {
    try {
      const statsKey = `${this.statsPrefix}${jobName}`;
      return await this.redisCacheService.get<CronJobStats>(statsKey);
    } catch (error) {
      this.logger.error(`Failed to get stats for job ${jobName}`, error.stack);
      return null;
    }
  }

  /**
   * Get all job statistics
   */
  async getAllJobStats(): Promise<CronJobStats[]> {
    try {
      const keys = await this.redisCacheService.keys(`${this.statsPrefix}*`);
      const stats: CronJobStats[] = [];

      for (const key of keys) {
        const stat = await this.redisCacheService.get<CronJobStats>(key);
        if (stat) {
          stats.push(stat as CronJobStats);
        }
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get all job stats', error.stack);
      return [];
    }
  }

  /**
   * Get job metrics for monitoring
   */
  async getJobMetrics(jobName: string): Promise<CronJobMetrics> {
    try {
      const stats = await this.getJobStats(jobName);
      if (!stats) {
        return this.getEmptyMetrics(jobName);
      }

      const successRate =
        stats.totalExecutions > 0
          ? (stats.successfulExecutions / stats.totalExecutions) * 100
          : 0;

      const errorRate =
        stats.totalExecutions > 0
          ? (stats.failedExecutions / stats.totalExecutions) * 100
          : 0;

      // Calculate throughput (jobs per minute in last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentExecutions = await this.getRecentExecutions(
        jobName,
        oneHourAgo,
      );
      const throughput = recentExecutions.length / 60; // per minute

      return {
        executionCount: stats.totalExecutions,
        successRate,
        averageExecutionTime: stats.averageExecutionTime,
        errorRate,
        throughput,
        queueSize: 0, // TODO: Implement queue monitoring
        memoryUsage: process.memoryUsage().heapUsed,
        cpuUsage: 0, // TODO: Implement CPU monitoring
      };
    } catch (error) {
      this.logger.error(
        `Failed to get metrics for job ${jobName}`,
        error.stack,
      );
      return this.getEmptyMetrics(jobName);
    }
  }

  /**
   * Get recent job executions
   */
  async getRecentExecutions(
    jobName: string,
    since: Date,
  ): Promise<CronJobExecution[]> {
    try {
      const keys = await this.redisCacheService.keys(
        `${this.executionPrefix}*`,
      );
      const executions: CronJobExecution[] = [];

      for (const key of keys) {
        const execution =
          await this.redisCacheService.get<CronJobExecution>(key);
        if (
          execution &&
          (execution as CronJobExecution).jobName === jobName &&
          (execution as CronJobExecution).startTime >= since
        ) {
          executions.push(execution as CronJobExecution);
        }
      }

      return executions.sort(
        (a, b) => b.startTime.getTime() - a.startTime.getTime(),
      );
    } catch (error) {
      this.logger.error(
        `Failed to get recent executions for job ${jobName}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Check system health
   */
  async getSystemHealth(): Promise<CronJobHealth> {
    try {
      const stats = await this.getAllJobStats();
      const totalJobs = stats.length;
      const activeJobs = stats.filter((s) => s.isRunning).length;

      // Calculate overall error rate
      const totalExecutions = stats.reduce(
        (sum, s) => sum + s.totalExecutions,
        0,
      );
      const totalFailures = stats.reduce(
        (sum, s) => sum + s.failedExecutions,
        0,
      );
      const errorRate =
        totalExecutions > 0 ? (totalFailures / totalExecutions) * 100 : 0;

      // Calculate average response time
      const avgResponseTime =
        stats.reduce((sum, s) => sum + s.averageExecutionTime, 0) / totalJobs ||
        0;

      const health: CronJobHealth = {
        isHealthy: errorRate < 10 && avgResponseTime < 30000, // 10% error rate, 30s avg time
        lastCheck: new Date(),
        uptime: process.uptime() * 1000,
        errorRate,
        averageResponseTime: avgResponseTime,
        activeJobs,
        totalJobs,
      };

      // Store health check
      await this.redisCacheService.set(
        `${this.healthPrefix}system`,
        health,
        300, // 5 minutes
      );

      return health;
    } catch (error) {
      this.logger.error('Failed to get system health', error.stack);
      return {
        isHealthy: false,
        lastCheck: new Date(),
        uptime: 0,
        errorRate: 100,
        averageResponseTime: 0,
        activeJobs: 0,
        totalJobs: 0,
      };
    }
  }

  /**
   * Check for alerts based on execution results
   */
  private async checkForAlerts(
    jobName: string,
    execution: CronJobExecution,
  ): Promise<void> {
    const alerts: CronJobAlert[] = [];

    // Check for job failure
    if (!execution.success) {
      alerts.push({
        jobName,
        type: 'failure',
        severity: 'error',
        message: `Job ${jobName} failed: ${execution.error}`,
        timestamp: new Date(),
        metadata: {
          executionId: execution.jobId,
          retryCount: execution.retryCount,
        },
      });
    }

    // Check for timeout
    if (execution.duration && execution.duration > 300000) {
      // 5 minutes
      alerts.push({
        jobName,
        type: 'timeout',
        severity: 'warning',
        message: `Job ${jobName} took ${execution.duration}ms to complete`,
        timestamp: new Date(),
        metadata: {
          executionId: execution.jobId,
          duration: execution.duration,
        },
      });
    }

    // Check for high error rate
    const stats = await this.getJobStats(jobName);
    if (stats && stats.totalExecutions > 10) {
      const errorRate = (stats.failedExecutions / stats.totalExecutions) * 100;
      if (errorRate > 20) {
        // 20% error rate
        alerts.push({
          jobName,
          type: 'high_error_rate',
          severity: 'critical',
          message: `Job ${jobName} has ${errorRate.toFixed(1)}% error rate`,
          timestamp: new Date(),
          metadata: { errorRate, totalExecutions: stats.totalExecutions },
        });
      }
    }

    // Store and emit alerts
    for (const alert of alerts) {
      await this.storeAlert(alert);
      this.eventEmitter.emit('cron.job.alert', alert);
    }
  }

  /**
   * Store alert
   */
  private async storeAlert(alert: CronJobAlert): Promise<void> {
    try {
      const alertKey = `${this.alertPrefix}${alert.jobName}:${alert.timestamp.getTime()}`;
      await this.redisCacheService.set(alertKey, alert, 86400); // 24 hours
    } catch (error) {
      this.logger.error('Failed to store alert', error.stack);
    }
  }

  /**
   * Get recent alerts
   */
  async getRecentAlerts(hours: number = 24): Promise<CronJobAlert[]> {
    try {
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);
      const keys = await this.redisCacheService.keys(`${this.alertPrefix}*`);
      const alerts: CronJobAlert[] = [];

      for (const key of keys) {
        const alert = await this.redisCacheService.get<CronJobAlert>(key);
        if (alert && (alert as CronJobAlert).timestamp >= since) {
          alerts.push(alert as CronJobAlert);
        }
      }

      return alerts.sort(
        (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),
      );
    } catch (error) {
      this.logger.error('Failed to get recent alerts', error.stack);
      return [];
    }
  }

  /**
   * Clean up old data
   */
  async cleanupOldData(daysToKeep: number = 7): Promise<void> {
    try {
      const cutoff = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

      // Clean up old executions
      const executionKeys = await this.redisCacheService.keys(
        `${this.executionPrefix}*`,
      );
      for (const key of executionKeys) {
        const execution =
          await this.redisCacheService.get<CronJobExecution>(key);
        if (execution && (execution as CronJobExecution).startTime < cutoff) {
          await this.redisCacheService.del(key);
        }
      }

      // Clean up old alerts
      const alertKeys = await this.redisCacheService.keys(
        `${this.alertPrefix}*`,
      );
      for (const key of alertKeys) {
        const alert = await this.redisCacheService.get<CronJobAlert>(key);
        if (alert && (alert as CronJobAlert).timestamp < cutoff) {
          await this.redisCacheService.del(key);
        }
      }

      this.logger.log(`Cleaned up data older than ${daysToKeep} days`);
    } catch (error) {
      this.logger.error('Failed to cleanup old data', error.stack);
    }
  }

  /**
   * Get empty metrics for error cases
   */
  private getEmptyMetrics(jobName: string): CronJobMetrics {
    return {
      executionCount: 0,
      successRate: 0,
      averageExecutionTime: 0,
      errorRate: 0,
      throughput: 0,
      queueSize: 0,
      memoryUsage: 0,
      cpuUsage: 0,
    };
  }
}
