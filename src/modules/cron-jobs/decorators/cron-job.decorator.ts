import { SetMetadata } from '@nestjs/common';
import { CronJobMetadata } from '../interfaces/cron-job.interface';

export const CRON_JOB_METADATA_KEY = 'cronJobMetadata';

export interface CronJobOptions {
  name: string;
  schedule: string;
  timeZone?: string;
  metadata?: CronJobMetadata;
  enableLocking?: boolean;
  lockDuration?: number;
  enableMonitoring?: boolean;
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Enhanced cron job decorator with monitoring, locking, and metadata
 */
export function CronJob(options: CronJobOptions): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    // Store metadata for the job
    const metadata: CronJobMetadata = {
      description: options.metadata?.description || `Cron job: ${options.name}`,
      category: options.metadata?.category || 'custom',
      priority: options.metadata?.priority || 'medium',
      estimatedDuration: options.metadata?.estimatedDuration || 5000,
      resourceUsage: options.metadata?.resourceUsage || 'low',
      dependencies: options.metadata?.dependencies || [],
      tags: options.metadata?.tags || [],
    };

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...options,
      metadata,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for jobs that require distributed locking
 */
export function WithLock(lockDuration?: number): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      enableLocking: true,
      lockDuration: lockDuration || 300000, // 5 minutes default
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for jobs that require monitoring
 */
export function WithMonitoring(): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      enableMonitoring: true,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for high-priority jobs
 */
export function HighPriority(): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      metadata: {
        ...existingMetadata.metadata,
        priority: 'high',
      },
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for critical jobs
 */
export function Critical(): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      metadata: {
        ...existingMetadata.metadata,
        priority: 'critical',
      },
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for jobs with timeout
 */
export function WithTimeout(timeout: number): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      timeout,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for jobs with retry configuration
 */
export function WithRetry(
  maxRetries: number,
  retryDelay: number,
): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      maxRetries,
      retryDelay,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for resource-intensive jobs
 */
export function ResourceIntensive(): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      metadata: {
        ...existingMetadata.metadata,
        resourceUsage: 'high',
      },
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Decorator for long-running jobs
 */
export function LongRunning(estimatedDuration: number): MethodDecorator {
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const existingMetadata =
      Reflect.getMetadata(CRON_JOB_METADATA_KEY, target, propertyKey) || {};

    SetMetadata(CRON_JOB_METADATA_KEY, {
      ...existingMetadata,
      metadata: {
        ...existingMetadata.metadata,
        estimatedDuration,
      },
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}
