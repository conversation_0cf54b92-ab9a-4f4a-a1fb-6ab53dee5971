import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CronJobResult } from './interfaces/cron-job.interface';

@Injectable()
export class CronJobsUtilService {
  private readonly logger = new Logger(CronJobsUtilService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Execute a job with retry logic and proper error handling
   */
  async executeWithRetry<T>(
    jobName: string,
    jobFunction: () => Promise<T>,
    maxRetries?: number,
  ): Promise<CronJobResult> {
    const maxRetryAttempts =
      maxRetries ||
      this.configService.get('cronJobs.errorHandling.maxRetries') ||
      3;
    const retryDelay =
      this.configService.get('cronJobs.errorHandling.retryDelay') || 5000;

    for (let attempt = 1; attempt <= maxRetryAttempts; attempt++) {
      try {
        this.logger.log(
          `Executing job ${jobName} (attempt ${attempt}/${maxRetryAttempts})`,
        );

        const startTime = Date.now();
        const result = await jobFunction();
        const executionTime = Date.now() - startTime;

        this.logger.log(
          `Job ${jobName} completed successfully in ${executionTime}ms`,
        );

        return {
          success: true,
          message: `Job ${jobName} completed successfully`,
          processedCount: result as any,
        };
      } catch (error) {
        this.logger.error(
          `Job ${jobName} failed on attempt ${attempt}`,
          error.stack,
        );

        if (attempt === maxRetryAttempts) {
          this.logger.error(
            `Job ${jobName} failed after ${maxRetryAttempts} attempts`,
          );
          return {
            success: false,
            message: `Job ${jobName} failed after ${maxRetryAttempts} attempts`,
            error: error.message,
          };
        }

        // Wait before retrying
        await this.delay(retryDelay * attempt); // Exponential backoff
      }
    }

    return {
      success: false,
      message: `Job ${jobName} failed unexpectedly`,
    };
  }

  /**
   * Validate cron expression
   */
  validateCronExpression(expression: string): boolean {
    const cronRegex =
      /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/;
    return cronRegex.test(expression);
  }

  /**
   * Get next execution time for a cron expression
   */
  getNextExecutionTime(cronExpression: string): Date | null {
    try {
      // This is a simplified implementation
      // In production, you might want to use a library like 'cron-parser'
      const now = new Date();
      const [minute, hour, day, month, dayOfWeek] = cronExpression.split(' ');

      const next = new Date(now);

      // Simple logic to calculate next execution
      // This is a basic implementation and might not handle all edge cases
      if (minute !== '*') {
        next.setMinutes(parseInt(minute));
      }
      if (hour !== '*') {
        next.setHours(parseInt(hour));
      }
      if (day !== '*') {
        next.setDate(parseInt(day));
      }

      // If the calculated time is in the past, move to next occurrence
      if (next <= now) {
        next.setDate(next.getDate() + 1);
      }

      return next;
    } catch (error) {
      this.logger.error('Error calculating next execution time', error);
      return null;
    }
  }

  /**
   * Format duration in human-readable format
   */
  formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Check if a job should be enabled based on configuration
   */
  isJobEnabled(jobName: string): boolean {
    const globalEnabled = this.configService.get('cronJobs.enabled');
    const jobEnabled = this.configService.get(
      `cronJobs.jobs.${jobName}.enabled`,
    );

    return globalEnabled && jobEnabled;
  }

  /**
   * Get job configuration
   */
  getJobConfig(jobName: string): any {
    return this.configService.get(`cronJobs.jobs.${jobName}`);
  }

  /**
   * Utility method to delay execution
   */
  delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Generate job statistics
   */
  generateJobStats(
    jobName: string,
    executionTime: number,
    success: boolean,
  ): any {
    return {
      jobName,
      executionTime,
      success,
      timestamp: new Date().toISOString(),
      formattedDuration: this.formatDuration(executionTime),
    };
  }

  /**
   * Validate job configuration
   */
  validateJobConfig(jobName: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = this.getJobConfig(jobName);

    if (!config) {
      errors.push(`Configuration not found for job: ${jobName}`);
      return { valid: false, errors };
    }

    if (config.schedule && !this.validateCronExpression(config.schedule)) {
      errors.push(`Invalid cron expression: ${config.schedule}`);
    }

    if (config.timeZone && !this.isValidTimeZone(config.timeZone)) {
      errors.push(`Invalid timezone: ${config.timeZone}`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if timezone is valid
   */
  private isValidTimeZone(timeZone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone });
      return true;
    } catch (error) {
      return false;
    }
  }
}
