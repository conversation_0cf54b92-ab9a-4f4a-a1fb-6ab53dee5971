import { Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CronJobMonitoringService } from '../services/cron-job-monitoring.service';
import { CronJobLockService } from '../services/cron-job-lock.service';
import { CronJobsUtilService } from '../cron-jobs.util.service';
import { CRON_JOB_METADATA_KEY } from '../decorators/cron-job.decorator';
import {
  CronJobExecution,
  CronJobResult,
} from '../interfaces/cron-job.interface';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class CronJobExecutionInterceptor {
  private readonly logger = new Logger(CronJobExecutionInterceptor.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly monitoringService: CronJobMonitoringService,
    private readonly lockService: CronJobLockService,
    private readonly utilService: CronJobsUtilService,
  ) {}

  /**
   * Execute a cron job with full monitoring and locking
   */
  async executeWithMonitoring(
    target: any,
    propertyKey: string,
    originalMethod: (...args: any[]) => Promise<any>,
    args: any[],
  ): Promise<CronJobResult> {
    const metadata = this.reflector.get(CRON_JOB_METADATA_KEY, target);
    const jobName = metadata?.name || propertyKey;
    const jobId = uuidv4();
    const startTime = new Date();

    // Create execution record
    const execution: CronJobExecution = {
      jobId,
      jobName,
      startTime,
      success: false,
      retryCount: 0,
      metadata: {
        method: propertyKey,
        instanceId: this.lockService.getInstanceId(),
        ...metadata,
      },
    };

    let lockId: string | null = null;

    try {
      this.logger.log(`Starting job execution: ${jobName} (${jobId})`);

      // Acquire lock if required
      if (metadata?.enableLocking) {
        lockId = await this.lockService.acquireLock(
          jobName,
          metadata.lockDuration || 300000,
        );

        if (!lockId) {
          const result: CronJobResult = {
            success: false,
            message: `Job ${jobName} is already running`,
            jobId,
            timestamp: startTime.toISOString(),
          };
          return result;
        }

        this.logger.debug(`Acquired lock for job ${jobName} (${lockId})`);
      }

      // Set timeout if specified
      let timeoutId: NodeJS.Timeout | null = null;
      if (metadata?.timeout) {
        timeoutId = setTimeout(() => {
          this.logger.error(
            `Job ${jobName} timed out after ${metadata.timeout}ms`,
          );
          throw new Error(
            `Job execution timed out after ${metadata.timeout}ms`,
          );
        }, metadata.timeout);
      }

      try {
        // Execute the job
        const result = await originalMethod.apply(target, args);

        // Clear timeout
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        // Update execution record
        execution.endTime = new Date();
        execution.duration = execution.endTime.getTime() - startTime.getTime();
        execution.success = true;
        execution.result = result;

        // Record execution if monitoring is enabled
        if (metadata?.enableMonitoring) {
          await this.monitoringService.recordExecution(execution);
        }

        this.logger.log(
          `Job ${jobName} completed successfully in ${execution.duration}ms`,
        );

        return {
          ...result,
          jobId,
          executionTime: execution.duration,
          timestamp: startTime.toISOString(),
        };
      } finally {
        // Clear timeout if not already cleared
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      }
    } catch (error) {
      // Update execution record for failure
      execution.endTime = new Date();
      execution.duration = execution.endTime.getTime() - startTime.getTime();
      execution.success = false;
      execution.error = error.message;

      this.logger.error(`Job ${jobName} failed: ${error.message}`, error.stack);

      // Record execution if monitoring is enabled
      if (metadata?.enableMonitoring) {
        await this.monitoringService.recordExecution(execution);
      }

      // Handle retries if configured
      if (metadata?.maxRetries && metadata.maxRetries > 0) {
        return await this.handleRetry(
          target,
          propertyKey,
          originalMethod,
          args,
          metadata,
          execution,
        );
      }

      return {
        success: false,
        message: `Job ${jobName} failed: ${error.message}`,
        error: error.message,
        jobId,
        executionTime: execution.duration,
        timestamp: startTime.toISOString(),
      };
    } finally {
      // Release lock if acquired
      if (lockId) {
        await this.lockService.releaseLock(jobName, lockId);
        this.logger.debug(`Released lock for job ${jobName} (${lockId})`);
      }
    }
  }

  /**
   * Handle job retry logic
   */
  private async handleRetry(
    target: any,
    propertyKey: string,
    originalMethod: (...args: any[]) => Promise<any>,
    args: any[],
    metadata: any,
    originalExecution: CronJobExecution,
  ): Promise<CronJobResult> {
    const jobName = metadata.name || propertyKey;
    const maxRetries = metadata.maxRetries || 3;
    const retryDelay = metadata.retryDelay || 5000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(
          `Retrying job ${jobName} (attempt ${attempt}/${maxRetries})`,
        );

        // Wait before retry (exponential backoff)
        if (attempt > 1) {
          const delay = retryDelay * Math.pow(2, attempt - 1);
          await this.utilService.delay(delay);
        }

        // Execute the job again
        const result = await originalMethod.apply(target, args);

        this.logger.log(`Job ${jobName} succeeded on retry attempt ${attempt}`);

        return {
          ...result,
          jobId: originalExecution.jobId,
          retryCount: attempt,
          timestamp: originalExecution.startTime.toISOString(),
        };
      } catch (error) {
        this.logger.error(
          `Job ${jobName} failed on retry attempt ${attempt}: ${error.message}`,
        );

        if (attempt === maxRetries) {
          // Final failure
          const finalExecution: CronJobExecution = {
            ...originalExecution,
            retryCount: attempt,
            error: error.message,
          };

          if (metadata?.enableMonitoring) {
            await this.monitoringService.recordExecution(finalExecution);
          }

          return {
            success: false,
            message: `Job ${jobName} failed after ${maxRetries} retry attempts`,
            error: error.message,
            jobId: originalExecution.jobId,
            retryCount: attempt,
            timestamp: originalExecution.startTime.toISOString(),
          };
        }
      }
    }

    // This should never be reached, but just in case
    return {
      success: false,
      message: `Job ${jobName} failed unexpectedly during retry process`,
      jobId: originalExecution.jobId,
      timestamp: originalExecution.startTime.toISOString(),
    };
  }

  /**
   * Execute a job with timeout
   */
  async executeWithTimeout(
    target: any,
    propertyKey: string,
    originalMethod: (...args: any[]) => Promise<any>,
    args: any[],
    timeout: number,
  ): Promise<CronJobResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Job execution timed out after ${timeout}ms`));
      }, timeout);

      originalMethod
        .apply(target, args)
        .then((result: CronJobResult) => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch((error: Error) => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Execute a job with resource monitoring
   */
  async executeWithResourceMonitoring(
    target: any,
    propertyKey: string,
    originalMethod: (...args: any[]) => Promise<any>,
    args: any[],
  ): Promise<CronJobResult> {
    const startMemory = process.memoryUsage();
    const startCpu = process.cpuUsage();

    try {
      const result = await originalMethod.apply(target, args);

      const endMemory = process.memoryUsage();
      const endCpu = process.cpuUsage();

      const memoryDelta = {
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external,
        rss: endMemory.rss - startMemory.rss,
      };

      const cpuDelta = {
        user: endCpu.user - startCpu.user,
        system: endCpu.system - startCpu.system,
      };

      this.logger.debug(`Job ${propertyKey} resource usage:`, {
        memory: memoryDelta,
        cpu: cpuDelta,
      });

      return result;
    } catch (error) {
      const endMemory = process.memoryUsage();
      const endCpu = process.cpuUsage();

      this.logger.error(`Job ${propertyKey} failed with resource usage:`, {
        memory: {
          heapUsed: endMemory.heapUsed - startMemory.heapUsed,
          heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        },
        cpu: {
          user: endCpu.user - startCpu.user,
          system: endCpu.system - startCpu.system,
        },
      });

      throw error;
    }
  }
}
