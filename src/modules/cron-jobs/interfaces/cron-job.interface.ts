export interface CronJobResult {
  success: boolean;
  message: string;
  processedCount?: number;
  error?: string;
  executionTime?: number;
  timestamp?: string;
  jobId?: string;
  retryCount?: number;
}

export interface CronJobConfig {
  enabled: boolean;
  schedule: string;
  timeZone: string;
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  concurrency?: number;
}

export interface CronJobStats {
  jobName: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecution: Date | null;
  lastSuccess: Date | null;
  lastFailure: Date | null;
  nextExecution: Date | null;
  isRunning: boolean;
}

export interface CronJobExecution {
  jobId: string;
  jobName: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  success: boolean;
  error?: string;
  result?: any;
  retryCount: number;
  metadata?: Record<string, any>;
}

export interface CronJobMetadata {
  description: string;
  category:
    | 'attendance'
    | 'payroll'
    | 'maintenance'
    | 'reporting'
    | 'health'
    | 'custom';
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number; // in milliseconds
  resourceUsage: 'low' | 'medium' | 'high';
  dependencies?: string[];
  tags?: string[];
}

export interface CronJobHealth {
  isHealthy: boolean;
  lastCheck: Date;
  uptime: number;
  errorRate: number;
  averageResponseTime: number;
  activeJobs: number;
  totalJobs: number;
}

export interface CronJobMetrics {
  executionCount: number;
  successRate: number;
  averageExecutionTime: number;
  errorRate: number;
  throughput: number; // jobs per minute
  queueSize: number;
  memoryUsage: number;
  cpuUsage: number;
}

export interface CronJobAlert {
  jobName: string;
  type: 'failure' | 'timeout' | 'high_error_rate' | 'performance_degradation';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface CronJobLock {
  jobName: string;
  lockId: string;
  acquiredAt: Date;
  expiresAt: Date;
  instanceId: string;
}

export interface CronJobQueue {
  jobName: string;
  priority: number;
  scheduledAt: Date;
  data?: any;
  retryCount: number;
  maxRetries: number;
}

export interface CronJobEnvironment {
  nodeEnv: string;
  instanceId: string;
  version: string;
  timezone: string;
  database: {
    connected: boolean;
    poolSize: number;
    activeConnections: number;
  };
  redis: {
    connected: boolean;
    memoryUsage: number;
  };
  system: {
    memoryUsage: number;
    cpuUsage: number;
    uptime: number;
  };
}
