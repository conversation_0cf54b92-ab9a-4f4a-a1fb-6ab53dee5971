import { registerAs } from '@nestjs/config';

export default registerAs('cronJobs', () => ({
  // Time zone for all cron jobs
  timeZone: process.env.CRON_TIMEZONE || 'UTC',

  // Enable/disable cron jobs globally
  enabled: process.env.CRON_JOBS_ENABLED === 'true' || true,

  // Individual job configurations
  jobs: {
    // Daily attendance reminder
    dailyAttendanceReminder: {
      enabled:
        process.env.CRON_DAILY_ATTENDANCE_REMINDER_ENABLED === 'true' || true,
      schedule:
        process.env.CRON_DAILY_ATTENDANCE_REMINDER_SCHEDULE || '0 9 * * *',
      timeZone: process.env.CRON_DAILY_ATTENDANCE_REMINDER_TIMEZONE || 'UTC',
    },

    // Auto clock-out
    autoClockOut: {
      enabled: process.env.CRON_AUTO_CLOCK_OUT_ENABLED === 'true' || true,
      schedule: process.env.CRON_AUTO_CLOCK_OUT_SCHEDULE || '0 * * * *',
      timeZone: process.env.CRON_AUTO_CLOCK_OUT_TIMEZONE || 'UTC',
      maxHours: parseInt(process.env.CRON_AUTO_CLOCK_OUT_MAX_HOURS) || 12,
    },

    // Weekly attendance report
    weeklyAttendanceReport: {
      enabled:
        process.env.CRON_WEEKLY_ATTENDANCE_REPORT_ENABLED === 'true' || true,
      schedule:
        process.env.CRON_WEEKLY_ATTENDANCE_REPORT_SCHEDULE || '0 8 * * 1',
      timeZone: process.env.CRON_WEEKLY_ATTENDANCE_REPORT_TIMEZONE || 'UTC',
    },

    // Monthly payroll processing
    monthlyPayrollProcessing: {
      enabled: process.env.CRON_MONTHLY_PAYROLL_ENABLED === 'true' || true,
      schedule: process.env.CRON_MONTHLY_PAYROLL_SCHEDULE || '0 2 1 * *',
      timeZone: process.env.CRON_MONTHLY_PAYROLL_TIMEZONE || 'UTC',
    },

    // Database cleanup
    databaseCleanup: {
      enabled: process.env.CRON_DATABASE_CLEANUP_ENABLED === 'true' || true,
      schedule: process.env.CRON_DATABASE_CLEANUP_SCHEDULE || '0 3 * * 0',
      timeZone: process.env.CRON_DATABASE_CLEANUP_TIMEZONE || 'UTC',
      retentionDays:
        parseInt(process.env.CRON_DATABASE_CLEANUP_RETENTION_DAYS) || 730, // 2 years
    },

    // Health check
    healthCheck: {
      enabled: process.env.CRON_HEALTH_CHECK_ENABLED === 'true' || true,
      schedule: process.env.CRON_HEALTH_CHECK_SCHEDULE || '*/5 * * * *',
      timeZone: process.env.CRON_HEALTH_CHECK_TIMEZONE || 'UTC',
    },
  },

  // Logging configuration
  logging: {
    enabled: process.env.CRON_LOGGING_ENABLED === 'true' || true,
    level: process.env.CRON_LOGGING_LEVEL || 'info',
  },

  // Error handling
  errorHandling: {
    maxRetries: parseInt(process.env.CRON_MAX_RETRIES) || 3,
    retryDelay: parseInt(process.env.CRON_RETRY_DELAY) || 5000, // 5 seconds
  },
}));
