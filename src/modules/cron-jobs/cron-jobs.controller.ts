import { Controller, Get, Post, Param, UseGuards, Query } from '@nestjs/common';
import { CronJobsService } from './cron-jobs.service';
import { CronJobMonitoringService } from './services/cron-job-monitoring.service';
import { CronJobLockService } from './services/cron-job-lock.service';
import { PermissionGuard } from '../auth/guards/permission.guard';
import { CanViewPlatform } from '../roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from '../../utils/constants';

@Controller('cron-jobs')
@UseGuards(PermissionGuard)
export class CronJobsController {
  constructor(
    private readonly cronJobsService: CronJobsService,
    private readonly monitoringService: CronJobMonitoringService,
    private readonly lockService: CronJobLockService,
  ) {}

  /**
   * Get all registered cron jobs
   */
  @Get()
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getAllJobs() {
    const jobs = this.cronJobsService.getAllJobs();
    const jobList = [];

    for (const [name, job] of jobs.entries()) {
      jobList.push({
        name,
        running: job.running,
        lastExecution: job.lastExecution,
        nextExecution: job.nextExecution,
        cronTime: job.cronTime.source,
      });
    }

    return {
      success: true,
      message: 'Cron jobs retrieved successfully',
      data: jobList,
    };
  }

  /**
   * Get status of a specific cron job
   */
  @Get(':jobName/status')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getJobStatus(@Param('jobName') jobName: string) {
    const status = this.cronJobsService.getJobStatus(jobName);

    return {
      success: true,
      message: `Status for job: ${jobName}`,
      data: {
        name: jobName,
        ...status,
      },
    };
  }

  /**
   * Start a specific cron job
   */
  @Post(':jobName/start')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async startJob(@Param('jobName') jobName: string) {
    const success = this.cronJobsService.startJob(jobName);

    return {
      success,
      message: success
        ? `Job ${jobName} started successfully`
        : `Failed to start job ${jobName}`,
    };
  }

  /**
   * Stop a specific cron job
   */
  @Post(':jobName/stop')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async stopJob(@Param('jobName') jobName: string) {
    const success = this.cronJobsService.stopJob(jobName);

    return {
      success,
      message: success
        ? `Job ${jobName} stopped successfully`
        : `Failed to stop job ${jobName}`,
    };
  }

  /**
   * Get job statistics
   */
  @Get(':jobName/stats')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getJobStats(@Param('jobName') jobName: string) {
    const stats = await this.monitoringService.getJobStats(jobName);

    return {
      success: true,
      message: `Statistics for job: ${jobName}`,
      data: stats,
    };
  }

  /**
   * Get job metrics
   */
  @Get(':jobName/metrics')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getJobMetrics(@Param('jobName') jobName: string) {
    const metrics = await this.monitoringService.getJobMetrics(jobName);

    return {
      success: true,
      message: `Metrics for job: ${jobName}`,
      data: metrics,
    };
  }

  /**
   * Get system health
   */
  @Get('health/system')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getSystemHealth() {
    const health = await this.monitoringService.getSystemHealth();

    return {
      success: true,
      message: 'System health retrieved successfully',
      data: health,
    };
  }

  /**
   * Get recent alerts
   */
  @Get('alerts/recent')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getRecentAlerts(@Query('hours') hours: string = '24') {
    const alerts = await this.monitoringService.getRecentAlerts(
      parseInt(hours),
    );

    return {
      success: true,
      message: `Recent alerts retrieved successfully`,
      data: alerts,
    };
  }

  /**
   * Get all active locks
   */
  @Get('locks/active')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async getActiveLocks() {
    const locks = await this.lockService.getAllLocks();

    return {
      success: true,
      message: 'Active locks retrieved successfully',
      data: locks,
    };
  }

  /**
   * Force release a lock
   */
  @Post('locks/:jobName/release')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  async forceReleaseLock(@Param('jobName') jobName: string) {
    const success = await this.lockService.forceReleaseLock(jobName);

    return {
      success,
      message: success
        ? `Lock for job ${jobName} released successfully`
        : `Failed to release lock for job ${jobName}`,
    };
  }
}
