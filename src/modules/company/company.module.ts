import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Company } from '../../models/company.model';
import { CompanyController } from './controllers/company.controller';
import { CompanyService } from './services/company.service';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { User } from 'src/models/users-models/user.model';
import { UsersModule } from '../users/users.module';
import { RolesModule } from 'src/modules/roles-and-permissions/roles/roles.module';
import { Role } from 'src/models/roles-and-permissions/role.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Company,
      EmploymentDetails,
      UserRole,
      User,
      Role,
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => RolesModule),
  ],
  controllers: [CompanyController],
  providers: [CompanyService],
  exports: [CompanyService],
})
export class CompanyModule {}
