import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PerformanceReview } from '../../models/performance-review.model';
import { PerformanceReviewsController } from './controllers/performance-reviews.controller';
import { PerformanceReviewsService } from './services/performance-reviews.service';

@Module({
  imports: [SequelizeModule.forFeature([PerformanceReview])],
  controllers: [PerformanceReviewsController],
  providers: [PerformanceReviewsService],
  exports: [PerformanceReviewsService],
})
export class PerformanceReviewsModule {}
