import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { PerformanceReviewsService } from '../services/performance-reviews.service';
import { PerformanceReview } from '../../../models/performance-review.model';
import { CreatePerformanceReviewDto } from '../dto/create-performance-review.dto';
import { UpdatePerformanceReviewDto } from '../dto/update-performance-review.dto';
// import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('performance-reviews')
// @UseGuards(JwtAuthGuard)
export class PerformanceReviewsController {
  constructor(
    private readonly performanceReviewsService: PerformanceReviewsService,
  ) {}

  @Post()
  async create(
    @Body() createPerformanceReviewDto: CreatePerformanceReviewDto,
  ): Promise<PerformanceReview> {
    return this.performanceReviewsService.create(createPerformanceReviewDto);
  }

  @Get()
  async findAll(
    @Query('status') status?: string,
    @Query('period') period?: string,
  ): Promise<PerformanceReview[]> {
    if (status) {
      return this.performanceReviewsService.findByStatus(status);
    }
    if (period) {
      return this.performanceReviewsService.findByPeriod(period);
    }
    return this.performanceReviewsService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<PerformanceReview[]> {
    return this.performanceReviewsService.findByEmployeeId(employeeId);
  }

  @Get('reviewer/:reviewerId')
  async findByReviewerId(
    @Param('reviewerId') reviewerId: number,
  ): Promise<PerformanceReview[]> {
    return this.performanceReviewsService.findByReviewerId(reviewerId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<PerformanceReview> {
    return this.performanceReviewsService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updatePerformanceReviewDto: UpdatePerformanceReviewDto,
  ): Promise<PerformanceReview> {
    return this.performanceReviewsService.update(
      id,
      updatePerformanceReviewDto,
    );
  }

  @Patch(':id/submit')
  async submit(@Param('id') id: number): Promise<PerformanceReview> {
    return this.performanceReviewsService.submit(id);
  }

  @Patch(':id/acknowledge')
  async acknowledge(
    @Param('id') id: number,
    @Body('employeeComments') employeeComments: string,
  ): Promise<PerformanceReview> {
    return this.performanceReviewsService.acknowledge(id, employeeComments);
  }

  @Patch(':id/complete')
  async complete(@Param('id') id: number): Promise<PerformanceReview> {
    return this.performanceReviewsService.complete(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.performanceReviewsService.remove(id);
  }
}
