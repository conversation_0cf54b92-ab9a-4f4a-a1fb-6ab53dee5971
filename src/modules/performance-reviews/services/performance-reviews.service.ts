import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { PerformanceReview } from '../../../models/performance-review.model';
import { CreatePerformanceReviewDto } from '../dto/create-performance-review.dto';
import { UpdatePerformanceReviewDto } from '../dto/update-performance-review.dto';

@Injectable()
export class PerformanceReviewsService {
  constructor(
    @InjectModel(PerformanceReview)
    private performanceReviewModel: typeof PerformanceReview,
  ) {}

  async create(
    createPerformanceReviewDto: CreatePerformanceReviewDto,
  ): Promise<PerformanceReview> {
    return this.performanceReviewModel.create({
      ...createPerformanceReviewDto,
    } as any);
  }

  async findAll(): Promise<PerformanceReview[]> {
    return this.performanceReviewModel.findAll({
      include: { all: true },
    });
  }

  async findByEmployeeId(employeeId: number): Promise<PerformanceReview[]> {
    return this.performanceReviewModel.findAll({
      where: { employeeId },
      include: { all: true },
      order: [['reviewDate', 'DESC']],
    });
  }

  async findByReviewerId(reviewerId: number): Promise<PerformanceReview[]> {
    return this.performanceReviewModel.findAll({
      where: { reviewerId },
      include: { all: true },
    });
  }

  async findByStatus(status: string): Promise<PerformanceReview[]> {
    return this.performanceReviewModel.findAll({
      where: { status },
      include: { all: true },
    });
  }

  async findByPeriod(period: string): Promise<PerformanceReview[]> {
    return this.performanceReviewModel.findAll({
      where: { period },
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<PerformanceReview> {
    const performanceReview = await this.performanceReviewModel.findByPk(id, {
      include: { all: true },
    });

    if (!performanceReview) {
      throw new NotFoundException(`Performance review with ID ${id} not found`);
    }

    return performanceReview;
  }

  async update(
    id: number,
    updatePerformanceReviewDto: UpdatePerformanceReviewDto,
  ): Promise<PerformanceReview> {
    const performanceReview = await this.findOne(id);
    await performanceReview.update(updatePerformanceReviewDto as any);
    return this.findOne(id);
  }

  async submit(id: number): Promise<PerformanceReview> {
    const performanceReview = await this.findOne(id);
    await performanceReview.update({ status: 'submitted' });
    return this.findOne(id);
  }

  async acknowledge(
    id: number,
    employeeComments: string,
  ): Promise<PerformanceReview> {
    const performanceReview = await this.findOne(id);
    await performanceReview.update({
      status: 'acknowledged',
      employeeComments,
      acknowledgedDate: new Date(),
    });
    return this.findOne(id);
  }

  async complete(id: number): Promise<PerformanceReview> {
    const performanceReview = await this.findOne(id);
    await performanceReview.update({ status: 'completed' });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const performanceReview = await this.findOne(id);
    await performanceReview.destroy();
  }
}
