import { PartialType } from '@nestjs/mapped-types';
import { CreatePerformanceReviewDto } from './create-performance-review.dto';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  IsDate,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

enum PerformanceStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  ACKNOWLEDGED = 'acknowledged',
  COMPLETED = 'completed',
}

export class UpdatePerformanceReviewDto extends PartialType(
  CreatePerformanceReviewDto,
) {
  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @IsOptional()
  @IsNumber()
  reviewerId?: number;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  reviewDate?: Date;

  @IsOptional()
  @IsString()
  period?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  performanceScore?: number;

  @IsOptional()
  @IsString()
  strengths?: string;

  @IsOptional()
  @IsString()
  areasForImprovement?: string;

  @IsOptional()
  @IsString()
  goals?: string;

  @IsOptional()
  @IsString()
  comments?: string;

  @IsOptional()
  @IsEnum(PerformanceStatus)
  status?: string;

  @IsOptional()
  @IsString()
  employeeComments?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  acknowledgedDate?: Date;
}
