import {
  IsString,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsEnum,
  IsDate,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

enum PerformanceStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  ACKNOWLEDGED = 'acknowledged',
  COMPLETED = 'completed',
}

export class CreatePerformanceReviewDto {
  @IsNumber()
  employeeId: number;

  @IsNumber()
  reviewerId: number;

  @Type(() => Date)
  @IsDate()
  reviewDate: Date;

  @IsString()
  period: string; // e.g., "Q1 2025", "H1 2025", "2025"

  @IsNumber()
  @Min(1)
  @Max(5)
  performanceScore: number;

  @IsString()
  strengths: string;

  @IsString()
  areasForImprovement: string;

  @IsOptional()
  @IsString()
  goals?: string;

  @IsOptional()
  @IsString()
  comments?: string;

  @IsOptional()
  @IsEnum(PerformanceStatus)
  status?: string = PerformanceStatus.DRAFT;

  @IsOptional()
  @IsString()
  employeeComments?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  acknowledgedDate?: Date;
}
