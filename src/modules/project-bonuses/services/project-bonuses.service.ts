import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { ProjectBonus } from '../../../models/project-bonus.model';

@Injectable()
export class ProjectBonusesService {
  constructor(
    @InjectModel(ProjectBonus)
    private projectBonusModel: typeof ProjectBonus,
  ) {}

  async create(createProjectBonusDto: any): Promise<ProjectBonus> {
    return this.projectBonusModel.create({ ...createProjectBonusDto });
  }

  async findAll(): Promise<ProjectBonus[]> {
    return this.projectBonusModel.findAll({
      include: { all: true },
    });
  }

  async findByEmployeeId(employeeId: number): Promise<ProjectBonus[]> {
    return this.projectBonusModel.findAll({
      where: { employeeId },
      include: { all: true },
    });
  }

  async findByProjectId(projectId: number): Promise<ProjectBonus[]> {
    return this.projectBonusModel.findAll({
      where: { projectId },
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<ProjectBonus> {
    return this.projectBonusModel.findByPk(id, {
      include: { all: true },
    });
  }

  async update(id: number, updateProjectBonusDto: any): Promise<ProjectBonus> {
    const projectBonus = await this.findOne(id);
    await projectBonus.update(updateProjectBonusDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const projectBonus = await this.findOne(id);
    await projectBonus.destroy();
  }
}
