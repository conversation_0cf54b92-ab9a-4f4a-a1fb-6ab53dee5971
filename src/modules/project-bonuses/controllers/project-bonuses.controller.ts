import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ProjectBonusesService } from '../services/project-bonuses.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ProjectBonus } from '../../../models/project-bonus.model';

@Controller('api/project-bonuses')
@UseGuards(JwtAuthGuard)
export class ProjectBonusesController {
  constructor(private readonly projectBonusesService: ProjectBonusesService) {}

  @Post()
  async create(@Body() createProjectBonusDto: any): Promise<ProjectBonus> {
    return this.projectBonusesService.create(createProjectBonusDto);
  }

  @Get()
  async findAll(): Promise<ProjectBonus[]> {
    return this.projectBonusesService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<ProjectBonus[]> {
    return this.projectBonusesService.findByEmployeeId(employeeId);
  }

  @Get('project/:projectId')
  async findByProjectId(
    @Param('projectId') projectId: number,
  ): Promise<ProjectBonus[]> {
    return this.projectBonusesService.findByProjectId(projectId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<ProjectBonus> {
    return this.projectBonusesService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateProjectBonusDto: any,
  ): Promise<ProjectBonus> {
    return this.projectBonusesService.update(id, updateProjectBonusDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.projectBonusesService.remove(id);
  }
}
