import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ProjectBonus } from '../../models/project-bonus.model';
import { ProjectBonusesController } from './controllers/project-bonuses.controller';
import { ProjectBonusesService } from './services/project-bonuses.service';

@Module({
  imports: [SequelizeModule.forFeature([ProjectBonus])],
  controllers: [ProjectBonusesController],
  providers: [ProjectBonusesService],
  exports: [ProjectBonusesService],
})
export class ProjectBonusesModule {}
