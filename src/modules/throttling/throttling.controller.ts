import { Controller, Get } from '@nestjs/common';
import { PublicRoute } from '../roles-and-permissions/permissions/decorators/permission.decorator';
import {
  SkipThrottle,
  ThrottleStrict,
  ThrottleNormal,
  ThrottleRelaxed,
} from './decorators/throttle.decorators';
import { ThrottlingService } from './throttling.service';

@Controller('throttling')
export class ThrottlingController {
  constructor(private readonly throttlingService: ThrottlingService) {}

  @Get('health')
  @PublicRoute()
  @SkipThrottle()
  healthCheck() {
    return this.throttlingService.getHealthStatus();
  }

  @Get('test/strict')
  @PublicRoute()
  @ThrottleNormal()
  testStrictThrottle() {
    this.throttlingService.incrementRequestCount();
    const data = this.throttlingService.getStrictThrottleInfo();
    return {
      message: `This endpoint has strict rate limiting (${data.configuration.limit} requests per ${data.configuration.ttl}ms)`,
      data: data,
    };
  }

  @Get('test/normal')
  @PublicRoute()
  @ThrottleNormal()
  testNormalThrottle() {
    this.throttlingService.incrementRequestCount();
    const data = this.throttlingService.getNormalThrottleInfo();
    return {
      message: `This endpoint has normal rate limiting (${data.configuration.limit} requests per ${data.configuration.ttl}ms)`,
      data: data,
    };
  }

  @Get('test/relaxed')
  @PublicRoute()
  @ThrottleRelaxed()
  testRelaxedThrottle() {
    this.throttlingService.incrementRequestCount();
    const data = this.throttlingService.getRelaxedThrottleInfo();
    return {
      message: `This endpoint has relaxed rate limiting (${data.configuration.limit} requests per ${data.configuration.ttl}ms)`,
      data: data,
    };
  }

  @Get('test/default')
  @PublicRoute()
  testDefaultThrottle() {
    this.throttlingService.incrementRequestCount();
    const data = this.throttlingService.getDefaultThrottleInfo();
    return {
      message: `This endpoint uses default rate limiting (${data.configuration.limit} requests per ${data.configuration.ttl}ms)`,
      data: data,
    };
  }

  @Get('info')
  @PublicRoute()
  @SkipThrottle()
  getThrottlingInfo() {
    const data = this.throttlingService.getAllThrottlingInfo();
    return {
      message: 'Dynamic Rate Limiting Configuration Information',
      data: data,
    };
  }

  @Get('config')
  @PublicRoute()
  @SkipThrottle()
  getConfigurationSummary() {
    const data = this.throttlingService.getConfigurationSummary();
    return {
      message: 'Rate Limiting Configuration Summary',
      data: data,
    };
  }
}
