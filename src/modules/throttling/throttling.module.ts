import { Module } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlingController } from './throttling.controller';
import { ThrottlingService } from './throttling.service';

@Module({
  imports: [
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => [
        {
          name: 'short',
          ttl: config.get('throttler.short.ttl'),
          limit: config.get('throttler.short.limit'),
        },
        {
          name: 'medium',
          ttl: config.get('throttler.medium.ttl'),
          limit: config.get('throttler.medium.limit'),
        },
        {
          name: 'long',
          ttl: config.get('throttler.long.ttl'),
          limit: config.get('throttler.long.limit'),
        },
        {
          name: 'default',
          ttl: config.get('throttler.default.ttl'),
          limit: config.get('throttler.default.limit'),
        },
      ],
    }),
  ],
  controllers: [ThrottlingController],
  providers: [ThrottlingService],
  exports: [ThrottlingService],
})
export class ThrottlingModule {}
