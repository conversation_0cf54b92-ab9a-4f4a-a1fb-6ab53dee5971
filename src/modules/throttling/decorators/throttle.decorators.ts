import { SkipThrottle, Throttle } from '@nestjs/throttler';

// Re-export the standard decorators for convenience
export { SkipThrottle, Throttle };

// Custom throttle decorators for different scenarios
export const ThrottleStrict = () =>
  Throttle({ short: { ttl: 1000, limit: 3 } });

export const ThrottleNormal = () =>
  Throttle({ medium: { ttl: 10000, limit: 20 } });

export const ThrottleRelaxed = () =>
  Throttle({ long: { ttl: 60000, limit: 100 } });

export const ThrottleDefault = () =>
  Throttle({ default: { ttl: 60000, limit: 10 } });
