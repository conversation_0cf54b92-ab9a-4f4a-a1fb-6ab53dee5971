import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ThrottleConfig } from './interfaces/throttle.config.interface';
import { SystemMetrics } from './interfaces/throttling.service.interface';
import { ThrottlingStats } from './interfaces/throttling.stats.interface';

@Injectable()
export class ThrottlingService {
  private requestCount = 0;
  private blockedCount = 0;
  private lastReset = new Date();

  constructor(private readonly configService: ConfigService) {}

  private getThrottleConfigs(): Record<string, ThrottleConfig> {
    const short = {
      name: 'short',
      ttl: this.configService.get<number>('throttler.short.ttl'),
      limit: this.configService.get<number>('throttler.short.limit'),
      description: 'Strict rate limiting for sensitive operations',
      requestsPerSecond: this.configService.get<number>(
        'throttler.short.limit',
      ),
      requestsPerMinute: Math.round(
        (this.configService.get<number>('throttler.short.limit') * 60) /
          (this.configService.get<number>('throttler.short.ttl') / 1000),
      ),
    };

    const medium = {
      name: 'medium',
      ttl: this.configService.get<number>('throttler.medium.ttl'),
      limit: this.configService.get<number>('throttler.medium.limit'),
      description: 'Balanced rate limiting for regular operations',
      requestsPerSecond: Math.round(
        this.configService.get<number>('throttler.medium.limit') /
          (this.configService.get<number>('throttler.medium.ttl') / 1000),
      ),
      requestsPerMinute: Math.round(
        (this.configService.get<number>('throttler.medium.limit') * 60) /
          (this.configService.get<number>('throttler.medium.ttl') / 1000),
      ),
    };

    const long = {
      name: 'long',
      ttl: this.configService.get<number>('throttler.long.ttl'),
      limit: this.configService.get<number>('throttler.long.limit'),
      description: 'Relaxed rate limiting for read operations',
      requestsPerSecond: Math.round(
        this.configService.get<number>('throttler.long.limit') /
          (this.configService.get<number>('throttler.long.ttl') / 1000),
      ),
      requestsPerMinute: this.configService.get<number>('throttler.long.limit'),
    };

    const defaultConfig = {
      name: 'default',
      ttl: this.configService.get<number>('throttler.default.ttl'),
      limit: this.configService.get<number>('throttler.default.limit'),
      description: 'Default rate limiting applied globally',
      requestsPerSecond: Math.round(
        this.configService.get<number>('throttler.default.limit') /
          (this.configService.get<number>('throttler.default.ttl') / 1000),
      ),
      requestsPerMinute: this.configService.get<number>(
        'throttler.default.limit',
      ),
    };

    return { short, medium, long, default: defaultConfig };
  }

  private getSystemMetrics(): SystemMetrics {
    const usage = process.cpuUsage();
    return {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: usage,
      processId: process.pid,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    };
  }

  private getThrottlingStats(): ThrottlingStats {
    return {
      totalRequests: this.requestCount,
      blockedRequests: this.blockedCount,
      activeThrottles: 0, // This would need to be tracked by the guard
      lastReset: this.lastReset.toISOString(),
    };
  }

  // Increment request counters (called by decorators or guards)
  incrementRequestCount() {
    this.requestCount++;
  }

  incrementBlockedCount() {
    this.blockedCount++;
  }

  getHealthStatus() {
    const metrics = this.getSystemMetrics();
    const configs = this.getThrottleConfigs();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'throttling',
      uptime: metrics.uptime,
      memory: {
        used: Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024),
        total: Math.round(metrics.memoryUsage.heapTotal / 1024 / 1024),
        external: Math.round(metrics.memoryUsage.external / 1024 / 1024),
        rss: Math.round(metrics.memoryUsage.rss / 1024 / 1024),
      },
      cpu: {
        user: Math.round(metrics.cpuUsage.user / 1000),
        system: Math.round(metrics.cpuUsage.system / 1000),
      },
      process: {
        pid: metrics.processId,
        nodeVersion: metrics.nodeVersion,
        platform: metrics.platform,
        arch: metrics.arch,
      },
      activeConfigurations: Object.keys(configs).length,
    };
  }

  getStrictThrottleInfo() {
    const config = this.getThrottleConfigs().short;
    const stats = this.getThrottlingStats();

    return {
      throttleType: 'strict',
      configuration: config,
      stats: {
        totalRequests: stats.totalRequests,
        blockedRequests: stats.blockedRequests,
        successRate:
          stats.totalRequests > 0
            ? (
                ((stats.totalRequests - stats.blockedRequests) /
                  stats.totalRequests) *
                100
              ).toFixed(2) + '%'
            : '0%',
      },
      description: config.description,
      recommendations: [
        'Use for login attempts and sensitive operations',
        'Monitor blocked requests for potential attacks',
        'Consider adjusting limits based on legitimate usage patterns',
      ],
      timestamp: new Date().toISOString(),
    };
  }

  getNormalThrottleInfo() {
    const config = this.getThrottleConfigs().medium;
    const stats = this.getThrottlingStats();

    return {
      throttleType: 'normal',
      configuration: config,
      stats: {
        totalRequests: stats.totalRequests,
        blockedRequests: stats.blockedRequests,
        successRate:
          stats.totalRequests > 0
            ? (
                ((stats.totalRequests - stats.blockedRequests) /
                  stats.totalRequests) *
                100
              ).toFixed(2) + '%'
            : '0%',
      },
      description: config.description,
      recommendations: [
        'Suitable for regular API operations',
        'Balances security with usability',
        'Monitor usage patterns for optimization',
      ],
      timestamp: new Date().toISOString(),
    };
  }

  getRelaxedThrottleInfo() {
    const config = this.getThrottleConfigs().long;
    const stats = this.getThrottlingStats();

    return {
      throttleType: 'relaxed',
      configuration: config,
      stats: {
        totalRequests: stats.totalRequests,
        blockedRequests: stats.blockedRequests,
        successRate:
          stats.totalRequests > 0
            ? (
                ((stats.totalRequests - stats.blockedRequests) /
                  stats.totalRequests) *
                100
              ).toFixed(2) + '%'
            : '0%',
      },
      description: config.description,
      recommendations: [
        'Ideal for read-heavy operations',
        'Allows higher throughput for legitimate users',
        'Still provides basic protection against abuse',
      ],
      timestamp: new Date().toISOString(),
    };
  }

  getDefaultThrottleInfo() {
    const config = this.getThrottleConfigs().default;
    const stats = this.getThrottlingStats();

    return {
      throttleType: 'default',
      configuration: config,
      stats: {
        totalRequests: stats.totalRequests,
        blockedRequests: stats.blockedRequests,
        successRate:
          stats.totalRequests > 0
            ? (
                ((stats.totalRequests - stats.blockedRequests) /
                  stats.totalRequests) *
                100
              ).toFixed(2) + '%'
            : '0%',
      },
      description: config.description,
      recommendations: [
        'Applied globally to all endpoints by default',
        'Provides baseline protection',
        'Consider using specific decorators for fine-tuned control',
      ],
      timestamp: new Date().toISOString(),
    };
  }

  getAllThrottlingInfo() {
    const configs = this.getThrottleConfigs();
    const metrics = this.getSystemMetrics();
    const stats = this.getThrottlingStats();

    return {
      system: {
        uptime: metrics.uptime,
        memory: {
          used: Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024) + ' MB',
          total:
            Math.round(metrics.memoryUsage.heapTotal / 1024 / 1024) + ' MB',
          external:
            Math.round(metrics.memoryUsage.external / 1024 / 1024) + ' MB',
          rss: Math.round(metrics.memoryUsage.rss / 1024 / 1024) + ' MB',
        },
        cpu: {
          user: Math.round(metrics.cpuUsage.user / 1000) + ' ms',
          system: Math.round(metrics.cpuUsage.system / 1000) + ' ms',
        },
        process: {
          pid: metrics.processId,
          nodeVersion: metrics.nodeVersion,
          platform: metrics.platform,
          arch: metrics.arch,
        },
      },
      configurations: {
        strict: {
          ...configs.short,
          usage: 'Sensitive operations (login, signup, password reset)',
          decorator: '@ThrottleNormal()',
        },
        normal: {
          ...configs.medium,
          usage: 'Regular API operations (profile, data updates)',
          decorator: '@ThrottleNormal()',
        },
        relaxed: {
          ...configs.long,
          usage: 'Read operations (list, search, reports)',
          decorator: '@ThrottleRelaxed()',
        },
        default: {
          ...configs.default,
          usage: 'Global default for all endpoints',
          decorator: 'Applied automatically',
        },
      },
      statistics: {
        ...stats,
        successRate:
          stats.totalRequests > 0
            ? (
                ((stats.totalRequests - stats.blockedRequests) /
                  stats.totalRequests) *
                100
              ).toFixed(2) + '%'
            : '0%',
        averageRequestsPerMinute:
          stats.totalRequests > 0
            ? Math.round(stats.totalRequests / (metrics.uptime / 60))
            : 0,
      },
      environment: {
        nodeEnv: this.configService.get<string>('NODE_ENV', 'development'),
        port: this.configService.get<number>('port', 3000),
        database: this.configService.get<string>('database.host', 'localhost'),
      },
      usage: {
        decorators: [
          '@SkipThrottle() - Skip rate limiting completely',
          '@ThrottleNormal() - Apply strict rate limiting (3 req/sec)',
          '@ThrottleNormal() - Apply normal rate limiting (2 req/sec)',
          '@ThrottleRelaxed() - Apply relaxed rate limiting (1.67 req/sec)',
          '@Throttle() - Apply custom rate limiting with specific config',
        ],
        endpoints: [
          'GET /api/throttling/health - Health check with system metrics',
          'GET /api/throttling/test/strict - Test strict rate limiting',
          'GET /api/throttling/test/normal - Test normal rate limiting',
          'GET /api/throttling/test/relaxed - Test relaxed rate limiting',
          'GET /api/throttling/test/default - Test default rate limiting',
          'GET /api/throttling/info - Get comprehensive throttling information',
        ],
        bestPractices: [
          'Use @ThrottleNormal() for authentication endpoints',
          'Use @ThrottleNormal() for data modification operations',
          'Use @ThrottleRelaxed() for read-only operations',
          'Monitor blocked requests to detect abuse patterns',
          'Adjust limits based on legitimate usage analytics',
        ],
      },
      timestamp: new Date().toISOString(),
    };
  }

  getConfigurationSummary() {
    const configs = this.getThrottleConfigs();

    return {
      timestamp: new Date().toISOString(),
      summary: {
        totalConfigurations: Object.keys(configs).length,
        strictestLimit: Math.min(...Object.values(configs).map((c) => c.limit)),
        mostPermissiveLimit: Math.max(
          ...Object.values(configs).map((c) => c.limit),
        ),
        shortestTTL: Math.min(...Object.values(configs).map((c) => c.ttl)),
        longestTTL: Math.max(...Object.values(configs).map((c) => c.ttl)),
      },
      configurations: Object.entries(configs).map(([key, config]) => ({
        name: key,
        limit: config.limit,
        ttl: config.ttl,
        requestsPerSecond: config.requestsPerSecond,
        requestsPerMinute: config.requestsPerMinute,
        description: config.description,
      })),
    };
  }
}
