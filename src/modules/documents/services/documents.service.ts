import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Document } from '../../../models/document.model';
import { CreateDocumentDto } from '../dto/create-document.dto';
import { UpdateDocumentDto } from '../dto/update-document.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { Op } from 'sequelize';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectModel(Document)
    private documentModel: typeof Document,
    private crudHelperService: CrudHelperService,
  ) {}

  async create(createDocumentDto: CreateDocumentDto): Promise<Document> {
    const createdDocument = await this.crudHelperService.create(
      this.documentModel,
      createDocumentDto,
    );
    return createdDocument as unknown as Document;
  }

  async findByEmployeeId(
    userId: number,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    rows: Document[];
    total: number;
    currentPage: number;
    totalPages: number;
    limit: number;
  }> {
    const offset = (page - 1) * limit;
    const { rows, count } = await this.documentModel.findAndCountAll({
      where: { userId },
      limit,
      offset,
      attributes: [
        'id',
        'userId',
        'name',
        'number',
        'documentUrl',
        'uploadedBy',
        'uploadedAt',
        'content',
        'issuingDate',
        'expiryDate',
      ],
    });
    const totalPages = Math.ceil(count / limit);
    return {
      rows: rows as unknown as Document[],
      total: count,
      currentPage: page,
      totalPages,
      limit,
    };
  }

  async findByType(type: string): Promise<Document[]> {
    const documents = await this.crudHelperService.findAll(this.documentModel, {
      where: { type },
      include: { all: true },
    });
    return documents as unknown as Document[];
  }

  async findOne(id: number): Promise<Document> {
    const document = await this.crudHelperService.findOne(this.documentModel, {
      where: { id },
      include: { all: true },
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document as unknown as Document;
  }

  async update(
    id: number,
    updateDocumentDto: UpdateDocumentDto,
  ): Promise<Document> {
    await this.crudHelperService.update(this.documentModel, updateDocumentDto, {
      where: { id },
    });
    const document = await this.crudHelperService.findOne(this.documentModel, {
      where: { id },
    });
    return document as unknown as Document;
  }

  async remove(id: number): Promise<void> {
    await this.crudHelperService.delete(this.documentModel, {
      where: { id },
    });
  }

  async findExpiring(days: number = 30): Promise<Document[]> {
    const today = new Date();
    const expiryThreshold = new Date();
    expiryThreshold.setDate(today.getDate() + days);

    const documents = await this.crudHelperService.findAll(this.documentModel, {
      where: {
        expiryDate: {
          [Op.lt]: expiryThreshold,
          [Op.gt]: today,
        },
      },
      include: { all: true },
    });
    return documents as unknown as Document[];
  }

  async findExpired(): Promise<Document[]> {
    const today = new Date();

    const documents = await this.crudHelperService.findAll(this.documentModel, {
      where: {
        expiryDate: {
          [Op.lt]: today,
        },
      },
      include: { all: true },
    });
    return documents as unknown as Document[];
  }
}
