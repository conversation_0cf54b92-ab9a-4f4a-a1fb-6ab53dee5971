import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
  Put,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UserRequestI } from '../../auth/auth.interfaces';
import { DocumentsService } from '../services/documents.service';
import { Document } from '../../../models/document.model';
import { CreateDocumentDto } from '../dto/create-document.dto';
import { UpdateDocumentDto } from '../dto/update-document.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';

@Controller('documents')
@UseGuards(PermissionGuard)
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async create(
    @Body() createDocumentDto: CreateDocumentDto,
    @Req() req: UserRequestI,
  ): Promise<Document> {
    const userId = req.user.id;
    createDocumentDto.userId = userId;
    return this.documentsService.create(createDocumentDto);
  }

  @Get('employee/:employeeId')
  @CanView(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async findByEmployeeId(
    @Param('employeeId', ParseIntPipe) employeeId: number,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ): Promise<{
    rows: Document[];
    total: number;
    currentPage: number;
    totalPages: number;
    limit: number;
  }> {
    console.log(employeeId);
    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.max(1, parseInt(limit, 10) || 10);
    return this.documentsService.findByEmployeeId(
      employeeId,
      pageNum,
      limitNum,
    );
  }

  @Get('expiring')
  @CanView(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async findExpiring(@Query('days') days: number = 30): Promise<Document[]> {
    return this.documentsService.findExpiring(days);
  }

  @Get('expired')
  @CanView(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async findExpired(): Promise<Document[]> {
    return this.documentsService.findExpired();
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDocumentDto: UpdateDocumentDto,
  ): Promise<Document> {
    return this.documentsService.update(id, updateDocumentDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.documentsService.remove(id);
  }
}
