import { IsString, IsDate, IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { DocumentType } from '../enums/doucument.type.enum';

export class UpdateDocumentDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(DocumentType)
  @IsOptional()
  type?: DocumentType;

  @IsString()
  @IsOptional()
  number?: string;

  @IsString()
  @IsOptional()
  frontSideDocumentUrl?: string;

  @IsString()
  @IsOptional()
  backSideDocumentUrl?: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  issuingDate?: Date;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  expiryDate?: Date;
}
