import { IsString, IsDate, IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { DocumentType } from '../enums/doucument.type.enum';

export class CreateDocumentDto {
  @IsString()
  name: string;

  @IsEnum(DocumentType)
  type: DocumentType;

  @IsString()
  number: string;

  @IsString()
  @IsOptional()
  frontSideDocumentUrl?: string;

  @IsString()
  @IsOptional()
  backSideDocumentUrl?: string;

  @Type(() => Date)
  @IsDate()
  issuingDate: Date;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  expiryDate?: Date;

  userId?: number;
}
