import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Payroll } from '../../../models/payroll.model';
import { CreatePayrollDto } from '../dto/create-payroll.dto';
import { UpdatePayrollDto } from '../dto/update-payroll.dto';

@Injectable()
export class PayrollsService {
  constructor(
    @InjectModel(Payroll)
    private payrollModel: typeof Payroll,
  ) {}

  async create(createPayrollDto: CreatePayrollDto): Promise<Payroll> {
    return this.payrollModel.create({ ...createPayrollDto } as any);
  }

  async findAll(): Promise<Payroll[]> {
    return this.payrollModel.findAll({
      include: { all: true },
    });
  }

  async findByEmployeeId(employeeId: number): Promise<Payroll[]> {
    return this.payrollModel.findAll({
      where: { employeeId },
      include: { all: true },
      order: [['paymentDate', 'DESC']],
    });
  }

  async findByStatus(status: string): Promise<Payroll[]> {
    return this.payrollModel.findAll({
      where: { status },
      include: { all: true },
    });
  }

  async findByPeriod(period: string): Promise<Payroll[]> {
    return this.payrollModel.findAll({
      where: { period },
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<Payroll> {
    const payroll = await this.payrollModel.findByPk(id, {
      include: { all: true },
    });

    if (!payroll) {
      throw new NotFoundException(`Payroll with ID ${id} not found`);
    }

    return payroll;
  }

  async update(
    id: number,
    updatePayrollDto: UpdatePayrollDto,
  ): Promise<Payroll> {
    const payroll = await this.findOne(id);
    await payroll.update(updatePayrollDto as any);
    return this.findOne(id);
  }

  async markAsPaid(id: number): Promise<Payroll> {
    const payroll = await this.findOne(id);
    await payroll.update({ status: 'paid' });
    return this.findOne(id);
  }

  async markAsCancelled(id: number): Promise<Payroll> {
    const payroll = await this.findOne(id);
    await payroll.update({ status: 'cancelled' });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const payroll = await this.findOne(id);
    await payroll.destroy();
  }
}
