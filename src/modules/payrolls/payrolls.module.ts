import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Payroll } from '../../models/payroll.model';
import { PayrollsController } from './controllers/payrolls.controller';
import { PayrollsService } from './services/payrolls.service';

@Module({
  imports: [SequelizeModule.forFeature([Payroll])],
  controllers: [PayrollsController],
  providers: [PayrollsService],
  exports: [PayrollsService],
})
export class PayrollsModule {}
