import {
  IsString,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsEnum,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';

enum PayrollStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PAID = 'paid',
  CANCELLED = 'cancelled',
}

export class CreatePayrollDto {
  @IsNumber()
  employeeId: number;

  @IsString()
  period: string;

  @IsNumber()
  grossAmount: number;

  @IsNumber()
  netAmount: number;

  @IsOptional()
  @IsString()
  allowances?: string;

  @IsOptional()
  @IsString()
  deductions?: string;

  @IsOptional()
  @IsString()
  bonuses?: string;

  @Type(() => Date)
  @IsDate()
  paymentDate: Date;

  @IsOptional()
  @IsEnum(PayrollStatus)
  status?: string = PayrollStatus.DRAFT;

  @IsOptional()
  @IsString()
  notes?: string;
}
