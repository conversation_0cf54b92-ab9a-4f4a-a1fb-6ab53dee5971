import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { PayrollsService } from '../services/payrolls.service';
import { Payroll } from '../../../models/payroll.model';
import { CreatePayrollDto } from '../dto/create-payroll.dto';
import { UpdatePayrollDto } from '../dto/update-payroll.dto';
// import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('payrolls')
// @UseGuards(JwtAuthGuard)
export class PayrollsController {
  constructor(private readonly payrollsService: PayrollsService) {}

  @Post()
  async create(@Body() createPayrollDto: CreatePayrollDto): Promise<Payroll> {
    return this.payrollsService.create(createPayrollDto);
  }

  @Get()
  async findAll(
    @Query('status') status?: string,
    @Query('period') period?: string,
  ): Promise<Payroll[]> {
    if (status) {
      return this.payrollsService.findByStatus(status);
    }
    if (period) {
      return this.payrollsService.findByPeriod(period);
    }
    return this.payrollsService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<Payroll[]> {
    return this.payrollsService.findByEmployeeId(employeeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Payroll> {
    return this.payrollsService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updatePayrollDto: UpdatePayrollDto,
  ): Promise<Payroll> {
    return this.payrollsService.update(id, updatePayrollDto);
  }

  @Patch(':id/paid')
  async markAsPaid(@Param('id') id: number): Promise<Payroll> {
    return this.payrollsService.markAsPaid(id);
  }

  @Patch(':id/cancelled')
  async markAsCancelled(@Param('id') id: number): Promise<Payroll> {
    return this.payrollsService.markAsCancelled(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.payrollsService.remove(id);
  }
}
