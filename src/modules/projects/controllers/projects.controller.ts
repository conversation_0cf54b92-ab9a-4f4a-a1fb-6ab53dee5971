import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ProjectsService } from '../services/projects.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Project } from '../../../models/project.model';
import { ProjectEmployee } from '../../../models/project-employee.model';

@Controller('projects')
@UseGuards(JwtAuthGuard)
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  async create(@Body() createProjectDto: any): Promise<Project> {
    return this.projectsService.create(createProjectDto);
  }

  @Get()
  async findAll(): Promise<Project[]> {
    return this.projectsService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Project> {
    return this.projectsService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateProjectDto: any,
  ): Promise<Project> {
    return this.projectsService.update(id, updateProjectDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.projectsService.remove(id);
  }

  @Post('employee')
  async addEmployeeToProject(@Body() data: any): Promise<ProjectEmployee> {
    return this.projectsService.addEmployeeToProject(data);
  }

  @Get('employee/:employeeId')
  async findProjectsByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<ProjectEmployee[]> {
    return this.projectsService.findProjectsByEmployeeId(employeeId);
  }

  @Delete('employee/:id')
  async removeEmployeeFromProject(@Param('id') id: number): Promise<void> {
    return this.projectsService.removeEmployeeFromProject(id);
  }
}
