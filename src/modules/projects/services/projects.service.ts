import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Project } from '../../../models/project.model';
import { ProjectEmployee } from '../../../models/project-employee.model';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectModel(Project)
    private projectModel: typeof Project,
    @InjectModel(ProjectEmployee)
    private projectEmployeeModel: typeof ProjectEmployee,
  ) {}

  async create(createProjectDto: any): Promise<Project> {
    return this.projectModel.create({ ...createProjectDto });
  }

  async findAll(): Promise<Project[]> {
    return this.projectModel.findAll({
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<Project> {
    return this.projectModel.findByPk(id, {
      include: { all: true },
    });
  }

  async update(id: number, updateProjectDto: any): Promise<Project> {
    const project = await this.findOne(id);
    await project.update(updateProjectDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const project = await this.findOne(id);
    await project.destroy();
  }

  async addEmployeeToProject(data: any): Promise<ProjectEmployee> {
    return this.projectEmployeeModel.create(data);
  }

  async findProjectsByEmployeeId(
    employeeId: number,
  ): Promise<ProjectEmployee[]> {
    return this.projectEmployeeModel.findAll({
      where: { employeeId },
      include: { all: true },
    });
  }

  async removeEmployeeFromProject(id: number): Promise<void> {
    const projectEmployee = await this.projectEmployeeModel.findByPk(id);
    await projectEmployee.destroy();
  }
}
