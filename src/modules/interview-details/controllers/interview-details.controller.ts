import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { InterviewDetailsService } from '../services/interview-details.service';
import { CreateInterviewDetailsDto } from '../dto/create-interview-details.dto';
import { UpdateInterviewDetailsDto } from '../dto/update-interview-details.dto';

@Controller('interview-details')
export class InterviewDetailsController {
  constructor(
    private readonly interviewDetailsService: InterviewDetailsService,
  ) {}

  @Post()
  create(@Body() createInterviewDetailsDto: CreateInterviewDetailsDto) {
    return this.interviewDetailsService.create(createInterviewDetailsDto);
  }

  @Get()
  findAll() {
    return this.interviewDetailsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.interviewDetailsService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateInterviewDetailsDto: UpdateInterviewDetailsDto,
  ) {
    return this.interviewDetailsService.update(+id, updateInterviewDetailsDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.interviewDetailsService.remove(+id);
  }
}
