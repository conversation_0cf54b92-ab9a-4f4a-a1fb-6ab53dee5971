import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { InterviewDetails } from '../../models/users-models/interview-details.model';
import { InterviewDetailsController } from './controllers/interview-details.controller';
import { InterviewDetailsService } from './services/interview-details.service';

@Module({
  imports: [SequelizeModule.forFeature([InterviewDetails])],
  controllers: [InterviewDetailsController],
  providers: [InterviewDetailsService],
  exports: [InterviewDetailsService],
})
export class InterviewDetailsModule {}
