import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { InterviewDetails } from '../../../models/users-models/interview-details.model';
import { CreateInterviewDetailsDto } from '../dto/create-interview-details.dto';
import { UpdateInterviewDetailsDto } from '../dto/update-interview-details.dto';

@Injectable()
export class InterviewDetailsService {
  constructor(
    @InjectModel(InterviewDetails)
    private interviewDetailsModel: typeof InterviewDetails,
  ) {}

  create(createInterviewDetailsDto: CreateInterviewDetailsDto) {
    return this.interviewDetailsModel.create({ ...createInterviewDetailsDto });
  }

  findAll() {
    return this.interviewDetailsModel.findAll();
  }

  findOne(id: number) {
    return this.interviewDetailsModel.findByPk(id);
  }

  async update(
    id: number,
    updateInterviewDetailsDto: UpdateInterviewDetailsDto,
  ) {
    const interviewDetails = await this.interviewDetailsModel.findByPk(id);
    if (interviewDetails) {
      return interviewDetails.update(updateInterviewDetailsDto);
    }
    return null;
  }

  async remove(id: number) {
    const interviewDetails = await this.interviewDetailsModel.findByPk(id);
    if (interviewDetails) {
      return interviewDetails.destroy();
    }
    return null;
  }
}
