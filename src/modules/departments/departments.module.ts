import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DepartmentsController } from './controllers/departments.controller';
import { DepartmentsService } from './services/departments.service';
import { Department } from '../../models/department.model';
import { EmploymentDetails } from '../../models/users-models/employment-details.model';
import { User } from '../../models/users-models/user.model';
import { CrudHelperService } from '../../common/crud-helper/crud-helper.service';
import { CompanyModule } from '../company/company.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Department, EmploymentDetails, User]),
    CompanyModule,
  ],
  controllers: [DepartmentsController],
  providers: [DepartmentsService, CrudHelperService],
  exports: [DepartmentsService],
})
export class DepartmentsModule {}
