import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { DepartmentsService } from '../services/departments.service';
import { Department } from '../../../models/department.model';
import { CreateDepartmentDto } from '../dto/create-department.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { AdminRequestI, UserRequestI } from 'src/modules/auth/auth.interfaces';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';

@Controller('departments')
@UseGuards(PermissionGuard)
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  findAllPaginated(
    @Req() req: UserRequestI,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<Department>>> {
    const user = req.user;
    return this.departmentsService.getDepartmentsPaginated(
      user.id,
      Number(page),
      Number(limit),
    );
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  findAll(@Req() req: UserRequestI): Promise<Partial<Department>[]> {
    return this.departmentsService.findAll(req.user);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Partial<Department>> {
    return this.departmentsService.findOne(id);
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  async create(
    @Body() createDepartmentDto: CreateDepartmentDto,
    @Req() req: AdminRequestI,
  ): Promise<{ message: string; data: Partial<Department> }> {
    const userId = req.user.id;
    const createdDepartment = await this.departmentsService.create(
      userId,
      createDepartmentDto,
    );

    return {
      message: 'Department created successfully',
      data: createdDepartment,
    };
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
  ): Promise<Partial<Department>> {
    return this.departmentsService.update(id, updateDepartmentDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.departmentsService.remove(id);
  }
}
