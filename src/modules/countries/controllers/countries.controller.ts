import { Controller, Get, Param, ParseIntPipe, Query } from '@nestjs/common';
import { CountriesService } from '../services/countries.service';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';

@Controller('countries')
export class CountriesController {
  constructor(private readonly countriesService: CountriesService) {}

  @Get()
  @ThrottleRelaxed()
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
  ) {
    const result = await this.countriesService.findAll(
      Number(page),
      Number(limit),
      search,
    );
    return result;
  }

  @Get('all')
  @ThrottleRelaxed()
  async getAllCountries() {
    const result = await this.countriesService.getAllCountries();

    return result;
  }

  @Get('search')
  @ThrottleRelaxed()
  async searchCountries(@Query('q') query: string) {
    const result = await this.countriesService.searchCountries(query);
    return result;
  }

  @Get('code/:countryCode')
  @ThrottleRelaxed()
  async findByCountryCode(@Param('countryCode') countryCode: string) {
    const result = await this.countriesService.findByCountryCode(countryCode);
    return result;
  }

  @Get(':id')
  @ThrottleRelaxed()
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const result = await this.countriesService.findOne(id);
    return result;
  }
}
