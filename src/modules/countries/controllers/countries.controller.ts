import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { CountriesService } from '../services/countries.service';
import { CreateCountryDto } from '../dto/create-country.dto';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';

@Controller('countries')
export class CountriesController {
  constructor(private readonly countriesService: CountriesService) {}

  @Get()
  @ThrottleRelaxed()
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
  ) {
    const result = await this.countriesService.findAll(
      Number(page),
      Number(limit),
      search,
    );
    return {
      success: true,
      message: 'Countries retrieved successfully',
      result,
    };
  }

  @Get('all')
  @ThrottleRelaxed()
  async getAllCountries() {
    const result = await this.countriesService.getAllCountries();
    return {
      success: true,
      message: 'All countries retrieved successfully',
      result,
    };
  }

  @Get('search')
  @ThrottleRelaxed()
  async searchCountries(@Query('q') query: string) {
    const result = await this.countriesService.searchCountries(query);
    return {
      success: true,
      message: 'Countries search completed successfully',
      data: result,
    };
  }

  @Get('code/:countryCode')
  @ThrottleRelaxed()
  async findByCountryCode(@Param('countryCode') countryCode: string) {
    const result = await this.countriesService.findByCountryCode(countryCode);
    return {
      success: true,
      message: 'Country retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @ThrottleRelaxed()
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const result = await this.countriesService.findOne(id);
    return {
      success: true,
      message: 'Country retrieved successfully',
      data: result,
    };
  }
}
