import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from '../../../models/address-models/country-model';
import { CreateCountryDto } from '../dto/create-country.dto';
import { UpdateCountryDto } from '../dto/update-country.dto';
import {
  CrudHelperService,
  PaginatedResult,
} from '../../../common/crud-helper/crud-helper.service';
import { Op } from 'sequelize';

@Injectable()
export class CountriesService {
  constructor(
    @InjectModel(Country)
    private countryModel: typeof Country,
    private crudHelperService: CrudHelperService,
  ) {}

  async create(createCountryDto: CreateCountryDto) {
    // Check if country with same name or country code already exists
    const existingCountry = await this.crudHelperService.findOne(
      this.countryModel,
      {
        where: {
          [Op.or]: [
            { name: createCountryDto.name },
            { countryCode: createCountryDto.countryCode },
          ],
        },
      },
    );

    if (existingCountry) {
      throw new ConflictException(
        'Country with this name or country code already exists',
      );
    }

    return this.crudHelperService.create(this.countryModel, createCountryDto);
  }

  async findAll(page: number = 1, limit: number = 10, search?: string) {
    const whereClause: any = {};

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { nationality: { [Op.iLike]: `%${search}%` } },
        { countryCode: { [Op.iLike]: `%${search}%` } },
      ];
    }

    return this.crudHelperService.paginateWithQuery(this.countryModel, {
      page,
      limit,
      where: whereClause,
      order: [['name', 'ASC']],
    });
  }

  async findOne(id: number) {
    const country = await this.crudHelperService.findOne(this.countryModel, {
      where: { id },
    });

    if (!country) {
      throw new NotFoundException(`Country with ID ${id} not found`);
    }

    return country;
  }

  async findByCountryCode(countryCode: string) {
    const country = await this.crudHelperService.findOne(this.countryModel, {
      where: { countryCode },
    });

    if (!country) {
      throw new NotFoundException(`Country with code ${countryCode} not found`);
    }

    return country;
  }

  async getAllCountries() {
    return this.crudHelperService.findAll(this.countryModel, {
      order: [['name', 'ASC']],
    });
  }

  async searchCountries(query: string) {
    return this.crudHelperService.findAll(this.countryModel, {
      where: {
        [Op.or]: [
          { name: { [Op.iLike]: `%${query}%` } },
          { nationality: { [Op.iLike]: `%${query}%` } },
          { countryCode: { [Op.iLike]: `%${query}%` } },
        ],
      },
      order: [['name', 'ASC']],
      limit: 20,
    });
  }
}
