import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CompanySettings } from '../../../models/company-settings.model';
import { OvertimeRule } from '../../../models/overtime-rule.model';

@Injectable()
export class CompanySettingsService {
  constructor(
    @InjectModel(CompanySettings)
    private companySettingsModel: typeof CompanySettings,
    @InjectModel(OvertimeRule)
    private overtimeRuleModel: typeof OvertimeRule,
  ) {}

  async findSettings(): Promise<CompanySettings> {
    const settings = await this.companySettingsModel.findOne();

    if (!settings) {
      throw new Error('Company settings not found');
    }

    return settings;
  }

  async updateSettings(
    id: number,
    updateSettingsDto: any,
  ): Promise<CompanySettings> {
    const settings = await this.companySettingsModel.findByPk(id);

    if (!settings) {
      throw new Error('Company settings not found');
    }

    await settings.update(updateSettingsDto);
    return settings;
  }

  async findOvertimeRules(): Promise<OvertimeRule[]> {
    return this.overtimeRuleModel.findAll();
  }

  async findOneOvertimeRule(id: number): Promise<OvertimeRule> {
    const overtimeRule = await this.overtimeRuleModel.findByPk(id);

    if (!overtimeRule) {
      throw new Error('Overtime rule not found');
    }

    return overtimeRule;
  }

  async createOvertimeRule(createOvertimeRuleDto: any): Promise<OvertimeRule> {
    return this.overtimeRuleModel.create(createOvertimeRuleDto);
  }

  async updateOvertimeRule(
    id: number,
    updateOvertimeRuleDto: any,
  ): Promise<OvertimeRule> {
    const overtimeRule = await this.overtimeRuleModel.findByPk(id);

    if (!overtimeRule) {
      throw new Error('Overtime rule not found');
    }

    await overtimeRule.update(updateOvertimeRuleDto);
    return overtimeRule;
  }

  async removeOvertimeRule(id: number): Promise<void> {
    const overtimeRule = await this.overtimeRuleModel.findByPk(id);

    if (!overtimeRule) {
      throw new Error('Overtime rule not found');
    }

    await overtimeRule.destroy();
  }
}
