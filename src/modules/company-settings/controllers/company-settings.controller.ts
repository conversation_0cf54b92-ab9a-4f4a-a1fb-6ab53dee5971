import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { CompanySettingsService } from '../services/company-settings.service';
import { CompanySettings } from '../../../models/company-settings.model';
import { OvertimeRule } from '../../../models/overtime-rule.model';

@Controller('company-settings')
export class CompanySettingsController {
  constructor(
    private readonly companySettingsService: CompanySettingsService,
  ) {}

  @Get()
  async findSettings(): Promise<CompanySettings> {
    return this.companySettingsService.findSettings();
  }

  @Patch(':id')
  async updateSettings(
    @Param('id') id: number,
    @Body() updateSettingsDto: any,
  ): Promise<CompanySettings> {
    return this.companySettingsService.updateSettings(id, updateSettingsDto);
  }

  @Get('overtime-rules')
  async findOvertimeRules(): Promise<OvertimeRule[]> {
    return this.companySettingsService.findOvertimeRules();
  }

  @Get('overtime-rules/:id')
  async findOneOvertimeRule(@Param('id') id: number): Promise<OvertimeRule> {
    return this.companySettingsService.findOneOvertimeRule(id);
  }

  @Post('overtime-rules')
  async createOvertimeRule(
    @Body() createOvertimeRuleDto: any,
  ): Promise<OvertimeRule> {
    return this.companySettingsService.createOvertimeRule(
      createOvertimeRuleDto,
    );
  }

  @Patch('overtime-rules/:id')
  async updateOvertimeRule(
    @Param('id') id: number,
    @Body() updateOvertimeRuleDto: any,
  ): Promise<OvertimeRule> {
    return this.companySettingsService.updateOvertimeRule(
      id,
      updateOvertimeRuleDto,
    );
  }

  @Delete('overtime-rules/:id')
  async removeOvertimeRule(@Param('id') id: number): Promise<void> {
    return this.companySettingsService.removeOvertimeRule(id);
  }
}
