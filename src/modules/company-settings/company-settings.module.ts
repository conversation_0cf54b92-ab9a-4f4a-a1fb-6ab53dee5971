import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CompanySettings } from '../../models/company-settings.model';
import { OvertimeRule } from '../../models/overtime-rule.model';
import { CompanySettingsController } from './controllers/company-settings.controller';
import { CompanySettingsService } from './services/company-settings.service';

@Module({
  imports: [SequelizeModule.forFeature([CompanySettings, OvertimeRule])],
  controllers: [CompanySettingsController],
  providers: [CompanySettingsService],
  exports: [CompanySettingsService],
})
export class CompanySettingsModule {}
