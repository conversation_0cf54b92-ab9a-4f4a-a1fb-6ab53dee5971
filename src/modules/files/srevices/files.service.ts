import { Injectable, NotFoundException } from '@nestjs/common';
import * as FormData from 'form-data';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from 'src/config/config.interface';

@Injectable()
export class FilesService {
  constructor(private readonly configService: ConfigService) {}

  async uploadFile(
    file: any, // Replace with proper file type once available
    token: string,
    userId: number,
  ): Promise<{ uploadResult: any }> {
    if (!file || !file.buffer || !file.originalname) {
      throw new NotFoundException('Invalid or missing file data');
    }

    const formData = new FormData();
    formData.append('file', file.buffer, file.originalname);
    const fileStorageUrl =
      this.configService.get<AppConfig['fileStorage']>('fileStorage').url || '';
    try {
      const headers = {
        Authorization: `Bearer ${token}`,
        ...formData.getHeaders(),
      };

      const result = await axios.post(
        `${fileStorageUrl}/files/upload?userId=${userId}`,
        formData,
        { headers },
      );

      return { uploadResult: result.data };
    } catch (error) {
      console.log('the error comes from here', error);
      throw new NotFoundException(`File upload failed: ${error.message}`);
    }
  }
}
