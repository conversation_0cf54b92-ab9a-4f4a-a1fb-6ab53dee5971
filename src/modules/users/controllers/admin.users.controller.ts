import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Body } from '@nestjs/common';
import { UpdateUserDto } from '../dto/update-user.dto';
import { Delete } from '@nestjs/common';
import { Patch } from '@nestjs/common';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { UsersService } from '../services/users.service';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { AdminRequestI, UserRequestI } from 'src/modules/auth/auth.interfaces';
import { User } from 'src/models/users-models/user.model';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { CreateUserDto } from '../dto/create-user.dto';
import {
  CanCreate,
  CanCreatePlatform,
  CanDeletePlatform,
  CanEditPlatform,
  CanView,
  CanViewPlatform,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  ThrottleStrict,
  ThrottleNormal,
  ThrottleRelaxed,
} from '../../throttling/decorators/throttle.decorators';

@Controller('admin/users')
@UseGuards(PermissionGuard)
export class AdminUsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  findAllUsers() {
    return this.usersService.findAll();
  }

  @Get('paginated')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  findAllPaginated(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    return this.usersService.getAllUsersPaginated(Number(page), Number(limit));
  }

  @Get(':id')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Partial<User>> {
    return this.usersService.findOne(id);
  }

  @Post('onboarding')
  @CanCreatePlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @Req() req: AdminRequestI,
  ) {
    const currentUserId = req.user.id;

    const createdEmployee = await this.usersService.createUser(
      createUserDto,
      currentUserId,
    );

    return {
      message: 'User created successfully',
      data: createdEmployee,
    };
  }

  @Patch(':id')
  @CanEditPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<Partial<User>> {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  @CanDeletePlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.usersService.remove(id);
  }
}
