import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { UsersService } from '../services/users.service';
import {
  CanCreate,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from 'src/modules/throttling';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { CreateCandidateDto } from '../dto/create-candidate.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { CreateInterviewDetailsDto } from '../dto/create-interview-details.dto';
import { SendInterviewInvitationDto } from '../dto/send-interview-invitation.dto';
import { InvitationStatus } from '../enums/invitation-status.enum';
import { SendInvitationOnlyDto } from '../dto/send-invitation-only.dto';
import { UpdateInvitationStatusDTO } from '../dto/update-invitation-status.dto';
import { SendOfferLetterDto } from '../dto/send-offer-letter.dto';

@Controller('candidates')
@UseGuards(PermissionGuard)
export class CandidateController {
  constructor(private readonly usersService: UsersService) {}

  @Post('')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'resume', maxCount: 1 },
      { name: 'passport', maxCount: 1 },
    ]),
  )
  async createCandidate(
    @Body() createCandidateDto: CreateCandidateDto,
    @UploadedFiles()
    files: { resume?: any[]; passport?: any[] },
    @Req() req: UserRequestI,
  ) {
    // Log for debugging
    console.log('Headers:', req.headers);
    console.log('Files received:', {
      resume: files?.resume?.[0]?.originalname || 'No resume file',
      passport: files?.passport?.[0]?.originalname || 'No passport file',
    });
    console.log('Body:', createCandidateDto);

    // Extract JWT token
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;
    const currentUserId = req.user.id;

    // Prepare files object for service
    const fileObjects = {
      resume: files?.resume?.[0] || null,
      passport: files?.passport?.[0] || null,
    };

    // Proceed to create candidate
    const result = await this.usersService.createCandidateWithTransaction(
      createCandidateDto,
      currentUserId,
      fileObjects,
      token,
    );

    return {
      success: true,
      message: 'Candidate created successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async removeCandidate(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.removeCandidate(id);
  }

  @Post('interview-details')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createInterviewDetails(
    @Body() createInterviewDetailsDto: CreateInterviewDetailsDto,
  ) {
    return this.usersService.createInterviewDetails(createInterviewDetailsDto);
  }

  @Post('send-and-save-interview-invitation')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendAndSaveInterviewInvitation(
    @Body() sendInterviewInvitationDto: SendInterviewInvitationDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    return this.usersService.sendInterviewInvitationAndSave(
      sendInterviewInvitationDto,
      currentUserId,
    );
  }

  @Post('send-interview-invitation')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendInterviewInvitation(
    @Body() sendInterviewInvitationDto: SendInvitationOnlyDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    return this.usersService.sendInvitationOnly(
      sendInterviewInvitationDto,
      currentUserId,
    );
  }

  @Get('get')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getCandidates(
    @Req() req: UserRequestI,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
    @Query('status') status?: InvitationStatus,
    @Query('department') department?: string,
  ) {
    const currentUserId = req.user.id;
    const result = await this.usersService.getCandidatesWithInterviewDetails(
      currentUserId,
      Number(page),
      Number(limit),
      search,
      status,
      department,
    );

    return {
      success: true,
      message: 'Candidates retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getCandidateById(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.getCandidateById(id);
  }

  @Get('interviewers')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getInterviewers(@Req() req: UserRequestI) {
    const currentUserId = req.user.id;
    return this.usersService.getInterviewers(currentUserId);
  }

  @Post('send-invitation-only')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendInvitationOnly(
    @Body() sendInvitationOnlyDto: SendInvitationOnlyDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    const result = await this.usersService.sendInvitationOnly(
      sendInvitationOnlyDto,
      currentUserId,
    );

    return result;
  }

  @Get('interview-details')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getInterviewDetailsByStatus(
    @Req() req: UserRequestI,
    @Query('status') status?: InvitationStatus,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ) {
    const currentUserId = req.user.id;
    const result = await this.usersService.getInterviewDetailsByStatus(
      currentUserId,
      status,
      Number(page),
      Number(limit),
    );

    return {
      success: true,
      message: 'Interview details retrieved successfully',
      data: result,
    };
  }

  @Put('interview-details/:id/status')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateInvitationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() status: UpdateInvitationStatusDTO,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    console.log(status);
    const result = await this.usersService.updateInvitationStatus(
      id,
      status.status,
      currentUserId,
    );

    return result;
  }

  @Get('positions/get')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getPositions(@Req() req: UserRequestI) {
    const currentUserId = req.user.id;
    return this.usersService.getPositions(currentUserId);
  }

  @Post('send-offer-letter')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendOfferLetter(
    @Body() sendOfferLetterDto: SendOfferLetterDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;

    const result = await this.usersService.sendOfferLetter(
      sendOfferLetterDto.candidateId,
      currentUserId,
    );

    return {
      success: result.success,
      message: result.message,
    };
  }
}
