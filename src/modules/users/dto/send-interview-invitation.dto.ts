import {
  IsEmail,
  IsString,
  IsOptional,
  IsDateString,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SendInterviewInvitationDto {
  @IsInt()
  @Type(() => Number)
  candidateId: number;

  @IsString()
  @IsOptional()
  candidatePhone?: string;

  @IsDateString()
  interviewDate: string;

  @IsString()
  interviewTime: string;

  @IsString()
  interviewType: string; // Technical, HR Round, Managerial, etc.

  @IsString()
  interviewMode: string; // In-person, Online

  @IsString()
  @IsOptional()
  location?: string; // Office Address for in-person

  @IsString()
  @IsOptional()
  meetingLink?: string; // Meeting Link or Address

  @IsInt()
  @Type(() => Number)
  interviewerId: number;

  @IsString()
  @IsOptional()
  customMessage?: string; // Optional note to include in the email
}
