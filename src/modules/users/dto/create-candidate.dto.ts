import { IsEmail, IsInt, IsString, IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCandidateDto {
  @IsOptional()
  firstName: string;

  @IsString()
  @IsOptional()
  middleName?: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  email: string;

  @IsString()
  @IsOptional()
  gender?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  positionId?: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  departmentId?: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  countryId?: number;

  @IsOptional()
  phone: string;

  @IsString()
  @IsOptional()
  passportNumber?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  passportExpiryDate?: Date;
}
