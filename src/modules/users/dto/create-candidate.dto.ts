import {
  IsEmail,
  IsInt,
  IsString,
  IsOptional,
  IsEnum,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Position } from '../enums/position.enum';

export class CreateCandidateDto {
  @IsOptional()
  firstName: string;

  @IsString()
  @IsOptional()
  middleName?: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  email: string;

  @IsString()
  @IsOptional()
  gender?: string;

  @IsEnum(Position)
  @IsOptional()
  position?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  departmentId?: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  countryId?: number;

  @IsOptional()
  phone: string;

  @IsString()
  @IsOptional()
  passportNumber?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  passportExpiryDate?: Date;
}
