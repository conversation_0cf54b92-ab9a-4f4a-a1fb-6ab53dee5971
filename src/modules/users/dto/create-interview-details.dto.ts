import { Type } from 'class-transformer';
import { IsDate, IsInt, IsOptional, IsString } from 'class-validator';

export class CreateInterviewDetailsDto {
  @IsInt()
  candidateId: number;

  @IsInt()
  interviewerId: number;

  @IsDate()
  @Type(() => Date)
  interviewDate: Date;

  @IsOptional()
  @IsString()
  interviewTime?: string;

  @IsOptional()
  @IsString()
  interviewType?: string;

  @IsOptional()
  @IsString()
  interviewMode?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  meetingLink?: string;

  @IsString()
  @IsOptional()
  customMessage?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expectedJoiningDate?: Date;
}
