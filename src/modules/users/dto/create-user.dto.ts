import {
  IsEmail,
  <PERSON>E<PERSON>,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  IsDateString,
  IsN<PERSON>ber,
  MinLength,
} from 'class-validator';

export class CreateUserDto {
  // User fields
  @IsNotEmpty()
  @IsString()
  username: string;

  @IsOptional()
  @IsString()
  password: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  redirectTo: string;

  // Required role assignment
  @IsNotEmpty()
  @IsNumber()
  roleId: number;

  // Optional employment-related fields
  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;

  @IsOptional()
  @IsString()
  profileImage?: string;

  @IsOptional()
  @IsString()
  googleId?: string;

  @IsOptional()
  @IsString()
  googleAccessToken?: string;

  @IsOptional()
  @IsString()
  googleRefreshToken?: string;

  @IsOptional()
  googleTokenExpiry?: Date;

  @IsOptional()
  isEmailVerified?: boolean;
}
