import { Global, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../../models/users-models/user.model';
import { UsersController } from './controllers/users.controller';
import { UsersService } from './services/users.service';
import { UserRoleModule } from '../roles-and-permissions/user-role/user-role.module';
import { UserDetails } from 'src/models/users-models/user-details.model';
import { AdminUserRoleController } from '../roles-and-permissions/user-role/controllers/admin.user.role.controller';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { AuthModule } from '../auth/auth.module';
import { AdminUsersController } from './controllers/admin.users.controller';
import { PermissionsModule } from '../roles-and-permissions/permissions/permissions.module';
import { FilesModule } from '../files/files.module';
import { EmailModule } from '../email/email.module';
import { Candidate } from 'src/models/users-models/candidate.model';
import { InterviewDetails } from 'src/models/users-models/interview-details.model';
import { Compensation } from 'src/models/users-models/compensation.model';
import { Benefit } from 'src/models/users-models/benefits.model';
import { CandidateController } from './controllers/candidate.controller';

@Global()
@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      UserDetails,
      EmploymentDetails,
      Candidate,
      InterviewDetails,
      Compensation,
      Benefit,
    ]),
    UserRoleModule,
    PermissionsModule,
    FilesModule,
    EmailModule,
  ],
  controllers: [
    UsersController,
    AdminUserRoleController,
    AdminUsersController,
    CandidateController,
  ],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
