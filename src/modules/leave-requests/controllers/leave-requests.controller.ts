import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
  Put,
} from '@nestjs/common';
import { LeaveRequestsService } from '../services/leave-requests.service';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import {
  Can<PERSON>reate,
  CanDelete,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';

@Controller('leave-requests')
@UseGuards(PermissionGuard)
export class LeaveRequestsController {
  constructor(private readonly leaveRequestsService: LeaveRequestsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async create(
    @Req() req: UserRequestI,
    @Body() createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.create(createLeaveRequestDto, req.user.id);
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async findAllPaginated(
    @Req() req: UserRequestI,
    @Query() dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    return this.leaveRequestsService.findAllPaginated(req.user.id, dto);
  }

  @Get('/company/paginated')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async findAllPaginatedByCompany(
    @Req() req: UserRequestI,
    @Query() dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    return this.leaveRequestsService.findAllPaginatedByCompany(req.user, dto);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async findOne(@Param('id') id: number): Promise<LeaveRequest> {
    return this.leaveRequestsService.findOne(id);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async update(
    @Param('id') id: number,
    @Body() updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.update(id, updateLeaveRequestDto);
  }

  @Put(':id/approve')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async approve(
    @Param('id') id: number,
    @Req() req: UserRequestI,
    @Body('approverId') approverId: number,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.approve(id, req.user.id);
  }

  @Put(':id/reject')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async reject(
    @Param('id') id: number,
    @Req() req: UserRequestI,
    @Body('approverId') approverId: number,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.reject(id, req.user.id);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async remove(
    @Param('id') id: number,
    @Req() req: UserRequestI,
  ): Promise<void> {
    return this.leaveRequestsService.remove(id, req.user.id);
  }
}
