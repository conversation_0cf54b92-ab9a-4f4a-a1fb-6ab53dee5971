import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { LeaveRequest } from '../../models/leave-request.model';
import { EmploymentDetails } from '../../models/users-models/employment-details.model';
import { User } from '../../models/users-models/user.model';
import { LeaveRequestsController } from './controllers/leave-requests.controller';
import { LeaveRequestsService } from './services/leave-requests.service';

@Module({
  imports: [
    SequelizeModule.forFeature([LeaveRequest, EmploymentDetails, User]),
  ],
  controllers: [LeaveRequestsController],
  providers: [LeaveRequestsService],
  exports: [LeaveRequestsService],
})
export class LeaveRequestsModule {}
