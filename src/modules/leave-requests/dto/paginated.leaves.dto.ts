import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import { LeaveRequestStatuses } from '../interface/leave.request.statuses.interface';
import { LeaveRequestTypes } from '../interface/leave.request.types.interface';
import { Type } from 'class-transformer';

export class PaginatedLeavesDto {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit: number;

  @IsOptional()
  @IsEnum(LeaveRequestStatuses)
  status?: LeaveRequestStatuses;

  @IsOptional()
  @IsEnum(LeaveRequestTypes)
  type?: LeaveRequestTypes;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  userId?: number;
}
