import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { Op } from 'sequelize';
import { User } from '../../../models/users-models/user.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';

@Injectable()
export class LeaveRequestsService {
  constructor(
    @InjectModel(LeaveRequest)
    private leaveRequestModel: typeof LeaveRequest,
    private readonly crudHelperService: CrudHelperService,
  ) {}

  async create(
    createLeaveRequestDto: CreateLeaveRequestDto,
    userId: number,
  ): Promise<Partial<LeaveRequest>> {
    const result = await this.crudHelperService.create<LeaveRequest>(
      this.leaveRequestModel,
      {
        ...createLeaveRequestDto,
        userId,
      },
    );
    return result;
  }

  async findAllPaginated(
    userId: number,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {
      userId,
    };

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        page: dto.page,
        limit: dto.limit,
        where,
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async findAllPaginatedByCompany(
    user: RequestUserObjectI,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {};

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    if (dto.userId) {
      where.userId = dto.userId;
    }

    // Add company filtering if context is provided
    if (user.role.scope === ROLE_SCOPE_ENUM.COMPANY && user.companyId) {
      where['$user.companyId$'] = user.companyId;
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        page: dto.page,
        limit: dto.limit,
        where,
        include: [
          {
            model: User,
            as: 'user',
            required: true,
          },
          {
            model: User,
            as: 'approver',
          },
        ],
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async findByStatus(status: string): Promise<LeaveRequest[]> {
    const results = await this.crudHelperService.findAll<LeaveRequest>(
      this.leaveRequestModel,
      {
        where: { status },
        include: { all: true },
      },
    );
    return results as LeaveRequest[];
  }

  async findOne(id: number): Promise<LeaveRequest> {
    const leaveRequest = await this.crudHelperService.findOne<LeaveRequest>(
      this.leaveRequestModel,
      {
        where: { id },
        include: { all: true },
      },
    );

    if (!leaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    return leaveRequest as LeaveRequest;
  }

  async update(
    id: number,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    await this.crudHelperService.update<LeaveRequest>(
      this.leaveRequestModel,
      updateLeaveRequestDto,
      { where: { id } },
    );
    return this.findOne(id);
  }

  async approve(id: number, approverId: number): Promise<LeaveRequest> {
    await this.crudHelperService.update<LeaveRequest>(
      this.leaveRequestModel,
      {
        status: 'approved',
        approvedBy: approverId,
      },
      { where: { id } },
    );
    return this.findOne(id);
  }

  async reject(id: number, approverId: number): Promise<LeaveRequest> {
    await this.crudHelperService.update<LeaveRequest>(
      this.leaveRequestModel,
      {
        status: 'rejected',
        approvedBy: approverId,
      },
      { where: { id } },
    );
    return this.findOne(id);
  }

  async remove(id: number, userId: number): Promise<void> {
    const leaveRequest = await this.findOne(id);
    if (leaveRequest.userId !== userId) {
      throw new ForbiddenException(
        'You are not allowed to delete this leave request',
      );
    }
    await this.crudHelperService.delete<LeaveRequest>(this.leaveRequestModel, {
      where: { id },
    });
  }
}
