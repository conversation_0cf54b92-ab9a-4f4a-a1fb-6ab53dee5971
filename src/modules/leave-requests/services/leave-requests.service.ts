import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { Op } from 'sequelize';
import { TenantContextI } from 'src/common/types/tenant-context.types';
import { EmploymentDetails } from '../../../models/users-models/employment-details.model';
import { User } from '../../../models/users-models/user.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { TenantAwareBaseService } from 'src/common/services/tenant-aware-base.service';

@Injectable()
export class LeaveRequestsService extends TenantAwareBaseService {
  constructor(
    @InjectModel(LeaveRequest)
    private leaveRequestModel: typeof LeaveRequest,
    @InjectModel(EmploymentDetails)
    private employmentDetailsModel: typeof EmploymentDetails,
    crudHelperService: CrudHelperService,
  ) {
    super(crudHelperService);
  }

  async create(
    createLeaveRequestDto: CreateLeaveRequestDto,
    userId: number,
  ): Promise<LeaveRequest> {
    return this.leaveRequestModel.create({
      ...createLeaveRequestDto,
      userId,
    } as any);
  }

  async findAllPaginated(
    userId: number,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {
      userId,
    };

    if (dto.userId) {
      where.userId = dto.userId;
    }

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        include: { all: true },
        page: dto.page,
        limit: dto.limit,
        where,
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async findAllPaginatedByCompany(
    tenantContext: TenantContextI | null,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {};

    // Add filters based on DTO
    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    if (dto.userId) {
      where.userId = dto.userId;
    }

    // Use the base service for automatic tenant filtering
    return this.paginateWithTenantFiltering(
      this.leaveRequestModel,
      tenantContext,
      {
        model: this.leaveRequestModel,
        userRelation: 'user',
        page: dto.page,
        limit: dto.limit,
        additionalWhere: where,
        additionalIncludes: [
          {
            model: User,
            as: 'approver',
          },
        ],
      },
    );
  }

  async findByStatus(status: string): Promise<LeaveRequest[]> {
    return this.leaveRequestModel.findAll({
      where: { status },
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<LeaveRequest> {
    const leaveRequest = await this.leaveRequestModel.findByPk(id, {
      include: { all: true },
    });

    if (!leaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    return leaveRequest;
  }

  async update(
    id: number,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.update(updateLeaveRequestDto as any);
    return this.findOne(id);
  }

  async approve(id: number, approverId: number): Promise<LeaveRequest> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.update({
      status: 'approved',
      approvedBy: approverId,
    });
    return this.findOne(id);
  }

  async reject(id: number, approverId: number): Promise<LeaveRequest> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.update({
      status: 'rejected',
      approvedBy: approverId,
    });
    return this.findOne(id);
  }

  async remove(id: number, userId: number): Promise<void> {
    const leaveRequest = await this.findOne(id);
    if (leaveRequest.userId !== userId) {
      throw new ForbiddenException(
        'You are not allowed to delete this leave request',
      );
    }
    await leaveRequest.destroy();
  }
}
