import { Modu<PERSON> } from '@nestjs/common';
import { EmployeesModule } from '../employees/employees.module';
import { TimeAttendanceModule } from '../time-attendance/time-attendance.module';
import { PayrollsModule } from '../payrolls/payrolls.module';
import { LeaveRequestsModule } from '../leave-requests/leave-requests.module';
import { ProjectsModule } from '../projects/projects.module';
import { DashboardService } from './services/dashboard.service';
import { AdminDashboardController } from './controllers/admin.dashboard.controller';
import { DashboardController } from './controllers/dashboard.controller';

@Module({
  imports: [
    EmployeesModule,
    TimeAttendanceModule,
    PayrollsModule,
    LeaveRequestsModule,
    ProjectsModule,
  ],
  controllers: [AdminDashboardController, DashboardController],
  providers: [DashboardService],
  exports: [DashboardService],
})
export class DashboardModule {}
