import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AssetsService } from './services/assets.service';
import { AssetsController } from './controllers/assets.controller';
import { Asset } from '../../models/asset.model';
import { User } from '../../models/users-models/user.model';
import { CrudHelperModule } from '../../common/crud-helper/crud-helper.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Asset, User]),
    CrudHelperModule,
  ],
  controllers: [AssetsController],
  providers: [AssetsService],
  exports: [AssetsService],
})
export class AssetsModule {}
