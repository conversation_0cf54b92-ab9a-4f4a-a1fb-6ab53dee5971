import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import { AssetsService } from '../services/assets.service';
import { CreateAssetDto } from '../dto/create-asset.dto';
import { UpdateAssetDto } from '../dto/update-asset.dto';
import { AssignAssetDto, UnassignAssetDto } from '../dto/assign-asset.dto';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  CanCreate,
  CanEdit,
  CanView,
  CanDelete,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { ThrottleRelaxed } from 'src/modules/throttling';

@Controller('assets')
@UseGuards(PermissionGuard)
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async create(
    @Body() createAssetDto: CreateAssetDto,
    @Req() req: UserRequestI,
  ) {
    const result = await this.assetsService.create(
      createAssetDto,
      req.user.companyId,
    );
    return {
      success: true,
      message: 'Asset created successfully',
      data: result,
    };
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findAll(
    @Req() req: UserRequestI,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
    @Query('search') search?: string,
    @Query('assetType') assetType?: string,
    @Query('status') status?: string,
    @Query('assignedTo', ParseIntPipe) assignedTo?: number,
  ) {
    const result = await this.assetsService.findAll(
      req.user.companyId,
      page,
      limit,
      search,
      assetType,
      status,
      assignedTo,
    );
    return {
      success: true,
      data: result,
    };
  }

  @Get('stats')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async getStats(@Req() req: UserRequestI) {
    const tenantContext = { companyId: req.user.companyId };
    const result = await this.assetsService.getAssetStats(tenantContext);
    return {
      success: true,
      data: result,
    };
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    const tenantContext = { companyId: req.user.companyId };
    const result = await this.assetsService.findOne(id, tenantContext);
    return {
      success: true,
      data: result,
    };
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAssetDto: UpdateAssetDto,
    @Req() req: UserRequestI,
  ) {
    const tenantContext = { companyId: req.user.companyId };
    const result = await this.assetsService.update(
      id,
      updateAssetDto,
      tenantContext,
    );
    return {
      success: true,
      message: 'Asset updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    const tenantContext = { companyId: req.user.companyId };
    await this.assetsService.remove(id, tenantContext);
    return {
      success: true,
      message: 'Asset deleted successfully',
    };
  }

  @Post(':id/assign')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async assignAsset(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignAssetDto: AssignAssetDto,
    @Req() req: UserRequestI,
  ) {
    const tenantContext = { companyId: req.user.companyId };
    const result = await this.assetsService.assignAsset(
      id,
      assignAssetDto,
      tenantContext,
    );
    return {
      success: true,
      message: 'Asset assigned successfully',
      data: result,
    };
  }

  @Post(':id/unassign')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async unassignAsset(
    @Param('id', ParseIntPipe) id: number,
    @Body() unassignAssetDto: UnassignAssetDto,
    @Req() req: UserRequestI,
  ) {
    const tenantContext = { companyId: req.user.companyId };
    const result = await this.assetsService.unassignAsset(
      id,
      unassignAssetDto,
      tenantContext,
    );
    return {
      success: true,
      message: 'Asset unassigned successfully',
      data: result,
    };
  }
}
