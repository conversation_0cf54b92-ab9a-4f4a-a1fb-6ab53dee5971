import {
  IsString,
  IsEnum,
  IsOptional,
  IsDateString,
  IsNumber,
  IsInt,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AssetType, AssetStatus } from '../../../models/asset.model';

export class CreateAssetDto {
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  assetTag: string;

  @IsEnum(AssetType)
  assetType: AssetType;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  modelSoftware?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  osVersion?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  licenseSerial?: string;

  @IsOptional()
  @IsDateString()
  purchaseDate?: Date;

  @IsOptional()
  @IsDateString()
  expirationDate?: Date;

  @IsOptional()
  @IsEnum(AssetStatus)
  status?: AssetStatus;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  assignedTo?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Type(() => Number)
  purchasePrice?: number;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  vendor?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  warrantyInfo?: string;

  @IsOptional()
  @IsDateString()
  warrantyExpiry?: Date;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  location?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  serialNumber?: string;
}
