import { WebSocketAdapter, INestApplication } from '@nestjs/common';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { ServerOptions, Server, Socket } from 'socket.io';
import { Observable } from 'rxjs';
import { MessageMappingProperties } from '@nestjs/websockets';

export class WebSocketIoAdapter extends IoAdapter implements WebSocketAdapter {
  constructor(
    private app: INestApplication,
    private corsOrigins: string | string[] = '*',
  ) {
    super(app);
  }

  createIOServer(port: number, options?: ServerOptions): Server {
    const server = super.createIOServer(port, {
      ...options,
      cors: {
        origin: this.corsOrigins,
        credentials: true,
      },
      pingInterval: 10000, // 10 seconds
      pingTimeout: 5000, // 5 seconds
    });

    // Enable the use of WebSockets without sticky sessions
    server.use((socket: Socket, next: (err?: Error) => void) => {
      // Implement any middleware here
      next();
    });

    return server;
  }

  // Implement required WebSocketAdapter methods
  create(port: number, options?: ServerOptions): Server {
    return this.createIOServer(port, options);
  }

  bindClientConnect(server: Server, callback: (socket: Socket) => void): void {
    server.on('connection', callback);
  }

  bindMessageHandlers(
    socket: Socket,
    handlers: MessageMappingProperties[],
    transform?: (data: any) => Observable<any>,
  ): void {
    handlers.forEach(({ message, callback }) => {
      socket.on(message, (data: any) => {
        if (transform) {
          transform(callback(data)).subscribe((response: any) => {
            socket.emit(message, response);
          });
        } else {
          socket.emit(message, callback(data));
        }
      });
    });
  }

  async close(server: Server): Promise<void> {
    server.close();
  }
}
