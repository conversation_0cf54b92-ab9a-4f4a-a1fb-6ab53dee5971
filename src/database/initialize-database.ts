import { Logger } from '@nestjs/common';
import { Sequelize } from 'sequelize-typescript';
import { modelsList } from 'src/models';

const logger = new Logger('Database Initialization');

export async function initializeDatabase(sequelize: Sequelize): Promise<void> {
  try {
    logger.log('Initializing database...');

    sequelize.addModels(modelsList);

    // Sync the database (create tables)
    await sequelize.sync();
    logger.log('Database sync completed successfully.');

    return;
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
}
