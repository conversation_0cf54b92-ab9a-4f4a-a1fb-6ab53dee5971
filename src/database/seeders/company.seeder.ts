import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Company } from 'src/models/company.model';
import { INDUSTRY_TYPES_ENUM } from 'src/modules/company/enum/industry.enum';

@Injectable()
export class CompanySeeder {
  constructor(
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed(): Promise<Company> {
    try {
      const [company] = await this.companyModel.findOrCreate({
        where: { name: 'Softbuilders LLC' },
        defaults: {
          name: 'Softbuilders LLC',
          portalName: 'softbuilders',
          industry: INDUSTRY_TYPES_ENUM.IT,
          email: '<EMAIL>',
          website: 'https://softbuilders.com',
        },
      });

      return company;
    } catch (error) {
      console.log('error', error);
      throw new Error(error);
    }
  }
}
