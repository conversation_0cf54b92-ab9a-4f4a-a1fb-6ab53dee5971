import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Position } from 'src/models/position.model';
import { Company } from 'src/models/company.model';

@Injectable()
export class PositionSeeder {
  constructor(
    @InjectModel(Position)
    private readonly positionModel: typeof Position,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed(): Promise<void> {
    try {
      // Get all companies
      const companies = await this.companyModel.findAll();

      // Default positions to create for each company
      const defaultPositions = [
        {
          title: 'Software Engineer',
          description: 'Develops and maintains software applications',
        },
        {
          title: 'Senior Software Engineer',
          description: 'Senior-level software development and mentoring',
        },
        {
          title: 'Tech Lead',
          description: 'Technical leadership and architecture decisions',
        },
        {
          title: 'Product Manager',
          description: 'Manages product development and strategy',
        },
        {
          title: 'Project Manager',
          description: 'Manages project timelines and deliverables',
        },
        {
          title: 'HR Specialist',
          description: 'Handles human resources and recruitment',
        },
        {
          title: 'Data Scientist',
          description: 'Analyzes data and builds predictive models',
        },
        {
          title: 'Business Analyst',
          description: 'Analyzes business requirements and processes',
        },
        {
          title: 'UI/UX Designer',
          description: 'Designs user interfaces and experiences',
        },
        {
          title: 'QA Engineer',
          description: 'Tests software quality and functionality',
        },
        {
          title: 'DevOps Engineer',
          description: 'Manages infrastructure and deployment pipelines',
        },
        {
          title: 'System Administrator',
          description: 'Maintains and configures system infrastructure',
        },
      ];

      // Create positions for each company
      for (const company of companies) {
        for (const positionData of defaultPositions) {
          await this.positionModel.findOrCreate({
            where: {
              title: positionData.title,
              companyId: company.id,
            },
            defaults: {
              ...positionData,
              companyId: company.id,
            },
          });
        }
      }

      console.log('Position seeding completed successfully');
    } catch (error) {
      console.error('Error seeding positions:', error);
      throw new Error(error);
    }
  }
}
