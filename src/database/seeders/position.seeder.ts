import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Position } from 'src/models/position.model';
import { Company } from 'src/models/company.model';

@Injectable()
export class PositionSeeder {
  constructor(
    @InjectModel(Position)
    private readonly positionModel: typeof Position,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed(): Promise<void> {
    try {
      // Get all companies
      const companies = await this.companyModel.findAll();

      // Default positions to create for each company
      const defaultPositions = [
        {
          title: 'Software Engineer',
          description: 'Develops and maintains software applications',
          level: 'Junior',
          minSalary: 50000,
          maxSalary: 80000,
        },
        {
          title: 'Senior Software Engineer',
          description: 'Senior-level software development and mentoring',
          level: 'Senior',
          minSalary: 80000,
          maxSalary: 120000,
        },
        {
          title: 'Tech Lead',
          description: 'Technical leadership and architecture decisions',
          level: 'Lead',
          minSalary: 100000,
          maxSalary: 150000,
        },
        {
          title: 'Product Manager',
          description: 'Manages product development and strategy',
          level: 'Manager',
          minSalary: 90000,
          maxSalary: 140000,
        },
        {
          title: 'Project Manager',
          description: 'Manages project timelines and deliverables',
          level: 'Manager',
          minSalary: 70000,
          maxSalary: 110000,
        },
        {
          title: 'HR Specialist',
          description: 'Handles human resources and recruitment',
          level: 'Specialist',
          minSalary: 45000,
          maxSalary: 75000,
        },
        {
          title: 'Data Scientist',
          description: 'Analyzes data and builds predictive models',
          level: 'Specialist',
          minSalary: 70000,
          maxSalary: 120000,
        },
        {
          title: 'Business Analyst',
          description: 'Analyzes business requirements and processes',
          level: 'Analyst',
          minSalary: 55000,
          maxSalary: 85000,
        },
        {
          title: 'UI/UX Designer',
          description: 'Designs user interfaces and experiences',
          level: 'Designer',
          minSalary: 50000,
          maxSalary: 90000,
        },
        {
          title: 'QA Engineer',
          description: 'Tests software quality and functionality',
          level: 'Engineer',
          minSalary: 45000,
          maxSalary: 75000,
        },
        {
          title: 'DevOps Engineer',
          description: 'Manages infrastructure and deployment pipelines',
          level: 'Engineer',
          minSalary: 70000,
          maxSalary: 110000,
        },
        {
          title: 'System Administrator',
          description: 'Maintains and configures system infrastructure',
          level: 'Administrator',
          minSalary: 50000,
          maxSalary: 80000,
        },
      ];

      // Create positions for each company
      for (const company of companies) {
        for (const positionData of defaultPositions) {
          await this.positionModel.findOrCreate({
            where: {
              title: positionData.title,
              companyId: company.id,
            },
            defaults: {
              ...positionData,
              companyId: company.id,
              isActive: true,
            },
          });
        }
      }

      console.log('Position seeding completed successfully');
    } catch (error) {
      console.error('Error seeding positions:', error);
      throw new Error(error);
    }
  }
}
