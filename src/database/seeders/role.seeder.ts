import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';

@Injectable()
export class RoleSeeder {
  constructor(
    @InjectModel(Role)
    private readonly RoleModel: typeof Role,
  ) {}

  async seed() {
    const roles = [
      {
        name: 'Super Admin',
        description: 'Has all permissions',
      },
      {
        name: 'Admin',
        description: 'Can have limited permission with delete access as well',
      },
    ];

    for (const role of roles) {
      await this.RoleModel.findOrCreate({
        where: { name: role.name },
        defaults: { ...role, scope: ROLE_SCOPE_ENUM.PLATFORM },
      });
    }
  }
}
