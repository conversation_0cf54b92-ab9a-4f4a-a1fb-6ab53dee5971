import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Company } from 'src/models/company.model';
import { Department } from 'src/models/department.model';

@Injectable()
export class DepartmentSeeder {
  constructor(
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed() {
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    if (!company) {
      throw new Error('Company not found');
    }

    const departments = [
      {
        name: 'Executive Management',
        description: 'Oversees overall company operations and strategy',
        companyId: company.id,
      },
      {
        name: 'Human Resources',
        description: 'Manages employee-related services',
        companyId: company.id,
      },
      {
        name: 'Technical Staff',
        description: 'Handles all technical development',
        companyId: company.id,
      },
      {
        name: 'Marketing',
        description: 'Responsible for brand and outreach',
        companyId: company.id,
      },
      {
        name: 'Finance',
        description: 'Handles financial records and reports',
        companyId: company.id,
      },
    ];

    for (const dept of departments) {
      await this.departmentModel.findOrCreate({
        where: { name: dept.name },
        defaults: dept,
      });
    }
  }
}
