import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from 'src/models/address-models/country-model';
import { User } from 'src/models/users-models/user.model';
import * as bcrypt from 'bcrypt';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';

const ADMIN_USERS: Partial<User>[] = [
  {
    email: '<EMAIL>',
    username: 'superadmin',
    firstName: 'Super',
    lastName: 'Admin',
  },
  {
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'System',
    lastName: 'Admin',
  },
];

const DEFAULT_PASSWORD = 'Admin@123';
const SALT_ROUNDS = 10;

@Injectable()
export class AdminUserSeeder {
  private readonly logger = new Logger(AdminUserSeeder.name);

  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
  ) {}

  async seed(): Promise<void> {
    const hashedPassword = await this.hashPassword();
    const roles = await this.getRoles();

    for (const adminUser of ADMIN_USERS) {
      await this.createAdminUser(adminUser, hashedPassword, roles);
    }
  }

  private async hashPassword(): Promise<string> {
    return bcrypt.hash(DEFAULT_PASSWORD, SALT_ROUNDS);
  }

  private async getRoles(): Promise<{ superAdmin: Role; admin: Role }> {
    const [superAdminRole, adminRole] = await Promise.all([
      this.roleModel.findOne({ where: { name: 'Super Admin' } }),
      this.roleModel.findOne({ where: { name: 'Admin' } }),
    ]);

    if (!superAdminRole || !adminRole) {
      throw new Error('Required roles not found');
    }

    return { superAdmin: superAdminRole, admin: adminRole };
  }

  private async createAdminUser(
    adminUser: Partial<User>,
    hashedPassword: string,
    roles: { superAdmin: Role; admin: Role },
  ): Promise<void> {
    // Check if user already exists
    let user = await this.userModel.findOne({
      where: { email: adminUser.email },
    });

    if (user) {
      // Update existing user
      await user.update({
        ...adminUser,
        password: hashedPassword,
      });
      this.logger.log(`Updated existing admin user: ${adminUser.email}`);
    } else {
      // Create new user
      user = await this.userModel.create({
        ...adminUser,
        password: hashedPassword,
      });
      this.logger.log(`Created new admin user: ${adminUser.email}`);
    }

    const role =
      adminUser.email === '<EMAIL>'
        ? roles.superAdmin
        : roles.admin;

    // Ensure user has the correct role
    await this.userRoleModel.findOrCreate({
      where: { userId: user.id, roleId: role.id },
    });
  }
}
