import { NestFactory } from '@nestjs/core';
import { AppModule } from 'src/app.module';

import { AdminUserSeeder } from './admin-user.seeder';
import { AdminPermissionSeeder } from './admin.permission.seeder';
import { CompanyRoleSeeder } from './company.role.seeder';
import { CompanySeeder } from './company.seeder';
import { CountrySeeder } from './country-seeder';
import { DepartmentSeeder } from './department-seeder';
import { PositionSeeder } from './position.seeder';
import { CandidateSeeder } from './candidate.seeder';
import { InterviewSeeder } from './interview.seeder';
import { CompensationSeeder } from './compensation.seeder';
import { AssetsSeeder } from './assets.seeder';
import { RoleSeeder } from './role.seeder';
import { SectionSeeder } from './section.seeder';
import { UserPermissionSeeder } from './user.permission.seeder';
import { PlatformUserSeeder } from './user.seeder';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  const sectionsSeeder = app.get(SectionSeeder);
  const companySeeder = app.get(CompanySeeder);
  const departmentSeeder = app.get(DepartmentSeeder);
  const positionSeeder = app.get(PositionSeeder);
  const adminRoleSeeder = app.get(RoleSeeder);
  const adminPermissionSeeder = app.get(AdminPermissionSeeder);
  const companyRoleSeeder = app.get(CompanyRoleSeeder);
  const userPermissionSeeder = app.get(UserPermissionSeeder);
  const countrySeeder = app.get(CountrySeeder);
  const adminUserSeeder = app.get(AdminUserSeeder);
  const platformUserSeeder = app.get(PlatformUserSeeder);
  const candidateSeeder = app.get(CandidateSeeder);
  const interviewSeeder = app.get(InterviewSeeder);
  const compensationSeeder = app.get(CompensationSeeder);
  const assetsSeeder = app.get(AssetsSeeder);

  await sectionsSeeder.seed();
  await companySeeder.seed();
  await departmentSeeder.seed();
  await positionSeeder.seed();
  await adminRoleSeeder.seed();
  await adminPermissionSeeder.seed();
  await companyRoleSeeder.seed();
  await userPermissionSeeder.seed();
  await countrySeeder.seed();
  await adminUserSeeder.seed();
  await platformUserSeeder.seed();
  await candidateSeeder.seed();
  await interviewSeeder.seed();
  await compensationSeeder.seed();
  await assetsSeeder.seed();

  await app.close();
}
bootstrap();
