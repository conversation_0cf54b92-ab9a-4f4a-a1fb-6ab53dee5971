import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { PlatformSection } from 'src/models/platform-section.model';

@Injectable()
export class AdminPermissionSeeder {
  constructor(
    @InjectModel(Permission)
    private readonly permissionModel: typeof Permission,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(PlatformSection)
    private readonly sectionModel: typeof PlatformSection,
  ) {}

  async seed() {
    try {
      const roles = await this.roleModel.findAll();
      const sections = await this.sectionModel.findAll();

      // Dynamically map roles and sections
      const roleMap = new Map<string, number>();
      roles.forEach((r) => roleMap.set(r.name.toLowerCase(), r.id));

      const sectionMap = new Map<string, number>();
      sections.forEach((s) => sectionMap.set(s.name.toLowerCase(), s.id));

      // Dynamically assign role variables
      const superAdmin = roleMap.get('super admin');
      const admin = roleMap.get('admin');

      if (!superAdmin || !admin) {
        throw new Error('Required roles not found.');
      }

      const permissions = [
        // Super Admin gets everything
        ...Array.from(sectionMap.entries()).map(([platformSectionName]) => ({
          roleId: superAdmin,
          platformSectionName,
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
        })),

        // Admin only gets Dashboard, Employees, Time Attendance and Settings
        {
          roleId: admin,
          platformSectionName: 'Dashboard',
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: false,
        },
        {
          roleId: admin,
          platformSectionName: 'Employees',
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: false,
        },
        {
          roleId: admin,
          platformSectionName: 'Time Attendence',
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: false,
        },
        {
          roleId: admin,
          platformSectionName: 'Settings',
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: false,
        },
      ];

      for (const perm of permissions) {
        const sectionId = sectionMap.get(
          perm.platformSectionName.toLowerCase(),
        );
        if (!sectionId) {
          console.warn(`Section not found: ${perm.platformSectionName}`);
          continue;
        }

        await this.permissionModel.findOrCreate({
          where: {
            roleId: perm.roleId,
            platformSectionId: sectionId,
          },
          defaults: {
            roleId: perm.roleId,
            platformSectionId: sectionId,
            canView: perm.canView,
            canCreate: perm.canCreate,
            canEdit: perm.canEdit,
            canDelete: perm.canDelete,
          },
        });
      }
    } catch (error) {
      console.error('Error seeding permissions:', error);
      throw error;
    }
  }
}
