import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { InterviewDetails } from 'src/models/users-models/interview-details.model';
import { Candidate } from 'src/models/users-models/candidate.model';
import { User } from 'src/models/users-models/user.model';
import { Company } from 'src/models/company.model';

@Injectable()
export class InterviewSeeder {
  constructor(
    @InjectModel(InterviewDetails)
    private readonly interviewModel: typeof InterviewDetails,
    @InjectModel(Candidate)
    private readonly candidateModel: typeof Candidate,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed(): Promise<void> {
    try {
      // Get the default company
      const company = await this.companyModel.findOne({
        where: { name: 'Softbuilders LLC' },
      });

      if (!company) {
        console.log('Company not found, skipping interview seeding');
        return;
      }

      // Get candidates from the company
      const candidates = await this.candidateModel.findAll({
        where: { companyId: company.id },
        limit: 3, // Only seed interviews for first 3 candidates
      });

      if (candidates.length === 0) {
        console.log('No candidates found, skipping interview seeding');
        return;
      }

      // Get users from the company who can be interviewers
      const interviewers = await this.userModel.findAll({
        where: { companyId: company.id },
        limit: 2, // Get first 2 users as potential interviewers
      });

      if (interviewers.length === 0) {
        console.log('No interviewers found, skipping interview seeding');
        return;
      }

      // Sample interview data
      const interviewsData = [
        {
          candidateId: candidates[0].id,
          interviewerId: interviewers[0].id,
          interviewDate: new Date('2024-08-15'),
          interviewTime: '10:00',
          interviewType: 'Technical',
          interviewMode: 'Online',
          location: 'Virtual Meeting Room',
          meetingLink: 'https://meet.google.com/abc-defg-hij',
          customMessage: 'Please prepare for technical questions about your experience.',
          invitationStatus: 'pending',
        },
        {
          candidateId: candidates[1].id,
          interviewerId: interviewers[1] ? interviewers[1].id : interviewers[0].id,
          interviewDate: new Date('2024-08-16'),
          interviewTime: '14:30',
          interviewType: 'HR',
          interviewMode: 'In-person',
          location: 'Office Conference Room A',
          meetingLink: '',
          customMessage: 'Looking forward to discussing your background and career goals.',
          invitationStatus: 'accepted',
        },
        {
          candidateId: candidates[2].id,
          interviewerId: interviewers[0].id,
          interviewDate: new Date('2024-08-17'),
          interviewTime: '11:00',
          interviewType: 'Final',
          interviewMode: 'Online',
          location: 'Virtual Meeting Room',
          meetingLink: 'https://meet.google.com/xyz-uvwx-rst',
          customMessage: 'Final round interview with the team lead.',
          invitationStatus: 'pending',
        },
      ];

      // Create interview details
      for (const interviewData of interviewsData) {
        await this.interviewModel.findOrCreate({
          where: { 
            candidateId: interviewData.candidateId,
            interviewDate: interviewData.interviewDate,
          },
          defaults: interviewData,
        });
      }

      console.log('Interview seeding completed successfully');
    } catch (error) {
      console.error('Error seeding interviews:', error);
      throw new Error(error);
    }
  }
}
