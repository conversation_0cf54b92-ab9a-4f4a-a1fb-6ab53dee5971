import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CompanyInternalScopeEnum,
  PlatformSection,
} from 'src/models/platform-section.model';
import { PANNEL_SECTIONS_NAMES_LIST } from 'src/utils/constants';
@Injectable()
export class SectionSeeder {
  constructor(
    @InjectModel(PlatformSection)
    private readonly sectionModel: typeof PlatformSection,
  ) {}

  async seed() {
    const sectionMap = new Map<string, number>();

    for (const {
      name,
      order,
      scope,
      category,
      companyInternalScope,
    } of PANNEL_SECTIONS_NAMES_LIST) {
      const [section] = await this.sectionModel.findOrCreate<PlatformSection>({
        where: { name },
        defaults: {
          name,
          scope,
          companyInternalScope,
          order,
          category,
        },
      });

      sectionMap.set(name, section.id);
    }

    return sectionMap;
  }
}
