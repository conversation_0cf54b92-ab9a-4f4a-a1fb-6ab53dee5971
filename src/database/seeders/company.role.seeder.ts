import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Company } from 'src/models/company.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';

@Injectable()
export class CompanyRoleSeeder {
  constructor(
    @InjectModel(Role)
    private readonly RoleModel: typeof Role,

    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed() {
    const roles = [
      {
        name: 'Company Super Admin',
        description: 'Has all permissions for the company',
      },
      {
        name: 'Company Admin',
        description: 'Can have limited permission with delete access as well',
      },
    ];

    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    for (const role of roles) {
      await this.RoleModel.findOrCreate({
        where: {
          name: role.name,
          companyId: company.id,
          scope: ROLE_SCOPE_ENUM.COMPANY,
        },
        defaults: {
          ...role,
          companyId: company.id,
          scope: ROLE_SCOPE_ENUM.COMPANY,
        },
      });
    }

    const createdRoles = await this.RoleModel.findAll({
      where: {
        companyId: company.id,
        scope: ROLE_SCOPE_ENUM.COMPANY,
      },
    });
  }
}
