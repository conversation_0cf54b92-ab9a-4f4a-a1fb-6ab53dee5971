import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from 'src/models/address-models/country-model';

@Injectable()
export class CountrySeeder {
  constructor(
    @InjectModel(Country)
    private readonly countryModel: typeof Country,
  ) {}

  async seed() {
    const countries = [
      { name: 'Pakistan', nationality: 'Pakistani', countryCode: 'PK' },
      { name: 'China', nationality: 'Chinese', countryCode: 'CN' },
      { name: 'United States', nationality: 'American', countryCode: 'US' },
      { name: 'United Kingdom', nationality: 'British', countryCode: 'GB' },
      {
        name: 'United Arab Emirates',
        nationality: 'Emirati',
        countryCode: 'AE',
      },
    ];

    for (const country of countries) {
      await this.countryModel.findOrCreate({
        where: { countryCode: country.countryCode },
        defaults: country,
      });
    }
  }
}
