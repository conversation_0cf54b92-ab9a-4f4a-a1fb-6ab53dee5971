import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Candidate } from 'src/models/users-models/candidate.model';
import { Company } from 'src/models/company.model';
import { Position } from 'src/models/position.model';
import { Department } from 'src/models/department.model';
import { Country } from 'src/models/address-models/country-model';

@Injectable()
export class CandidateSeeder {
  constructor(
    @InjectModel(Candidate)
    private readonly candidateModel: typeof Candidate,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(Position)
    private readonly positionModel: typeof Position,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(Country)
    private readonly countryModel: typeof Country,
  ) {}

  async seed(): Promise<void> {
    try {
      // Get the default company
      const company = await this.companyModel.findOne({
        where: { name: 'Softbuilders LLC' },
      });

      if (!company) {
        console.log('Company not found, skipping candidate seeding');
        return;
      }

      // Get departments
      const departments = await this.departmentModel.findAll({
        where: { companyId: company.id },
      });

      if (departments.length === 0) {
        console.log('No departments found, skipping candidate seeding');
        return;
      }

      // Get positions
      const positions = await this.positionModel.findAll({
        where: { companyId: company.id },
      });

      if (positions.length === 0) {
        console.log('No positions found, skipping candidate seeding');
        return;
      }

      // Get a country for testing
      const country = await this.countryModel.findOne();

      if (!country) {
        console.log('No countries found, skipping candidate seeding');
        return;
      }

      // Sample candidates data
      const candidatesData = [
        {
          firstName: 'John',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+1234567890',
          gender: 'male',
          departmentId: departments[0].id,
          positionId: positions[0].id,
          countryId: country.id,
          companyId: company.id,
          passportNumber: 'AB123456',
          passportExpiryDate: new Date('2025-12-31'),
        },
        {
          firstName: 'Emily',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+1987654321',
          gender: 'female',
          departmentId: departments[1].id,
          positionId: positions[1].id,
          countryId: country.id,
          companyId: company.id,
          passportNumber: 'CD789012',
          passportExpiryDate: new Date('2026-06-30'),
        },
        {
          firstName: 'Michael',
          lastName: 'Williams',
          email: '<EMAIL>',
          phone: '+1122334455',
          gender: 'male',
          departmentId: departments[2].id,
          positionId: positions[2].id,
          countryId: country.id,
          companyId: company.id,
          passportNumber: 'EF345678',
          passportExpiryDate: new Date('2024-09-15'),
        },
        {
          firstName: 'Sarah',
          lastName: 'Brown',
          email: '<EMAIL>',
          phone: '+1555666777',
          gender: 'female',
          departmentId: departments[0].id,
          positionId: positions[3].id,
          countryId: country.id,
          companyId: company.id,
          passportNumber: 'GH901234',
          passportExpiryDate: new Date('2025-03-20'),
        },
        {
          firstName: 'David',
          lastName: 'Jones',
          email: '<EMAIL>',
          phone: '+1888999000',
          gender: 'male',
          departmentId: departments[1].id,
          positionId: positions[4].id,
          countryId: country.id,
          companyId: company.id,
          passportNumber: 'IJ567890',
          passportExpiryDate: new Date('2026-11-10'),
        },
      ];

      // Create candidates
      for (const candidateData of candidatesData) {
        await this.candidateModel.findOrCreate({
          where: { email: candidateData.email },
          defaults: candidateData,
        });
      }

      console.log('Candidate seeding completed successfully');
    } catch (error) {
      console.error('Error seeding candidates:', error);
      throw new Error(error);
    }
  }
}
