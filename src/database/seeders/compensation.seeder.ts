import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Compensation } from 'src/models/users-models/compensation.model';
import { Candidate } from 'src/models/users-models/candidate.model';
import { Company } from 'src/models/company.model';

@Injectable()
export class CompensationSeeder {
  constructor(
    @InjectModel(Compensation)
    private readonly compensationModel: typeof Compensation,
    @InjectModel(Candidate)
    private readonly candidateModel: typeof Candidate,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed(): Promise<void> {
    try {
      // Get the default company
      const company = await this.companyModel.findOne({
        where: { name: 'Softbuilders LLC' },
      });

      if (!company) {
        console.log('Company not found, skipping compensation seeding');
        return;
      }

      // Get candidates from the company
      const candidates = await this.candidateModel.findAll({
        where: { companyId: company.id },
        limit: 3, // Only seed compensation for first 3 candidates
      });

      if (candidates.length === 0) {
        console.log('No candidates found, skipping compensation seeding');
        return;
      }

      // Sample compensation data
      const compensationData = [
        {
          candidateId: candidates[0].id,
          baseSalary: 75000,
          currency: 'USD',
          salaryType: 'annual',
          effectiveDate: new Date('2024-09-01'),
          notes: 'Initial offer for Software Engineer position',
        },
        {
          candidateId: candidates[1].id,
          baseSalary: 95000,
          currency: 'USD',
          salaryType: 'annual',
          effectiveDate: new Date('2024-09-15'),
          notes: 'Competitive offer for Senior Software Engineer',
        },
        {
          candidateId: candidates[2].id,
          baseSalary: 120000,
          currency: 'USD',
          salaryType: 'annual',
          effectiveDate: new Date('2024-10-01'),
          notes: 'Leadership role compensation package',
        },
      ];

      // Create compensation records
      for (const compData of compensationData) {
        await this.compensationModel.findOrCreate({
          where: { candidateId: compData.candidateId },
          defaults: compData,
        });
      }

      console.log('Compensation seeding completed successfully');
    } catch (error) {
      console.error('Error seeding compensation:', error);
      throw new Error(error);
    }
  }
}
