import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Compensation } from 'src/models/users-models/compensation.model';
import { Candidate } from 'src/models/users-models/candidate.model';
import { Company } from 'src/models/company.model';

@Injectable()
export class CompensationSeeder {
  constructor(
    @InjectModel(Compensation)
    private readonly compensationModel: typeof Compensation,
    @InjectModel(Candidate)
    private readonly candidateModel: typeof Candidate,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
  ) {}

  async seed(): Promise<void> {
    try {
      // Get the default company
      const company = await this.companyModel.findOne({
        where: { name: 'Softbuilders LLC' },
      });

      if (!company) {
        console.log('Company not found, skipping compensation seeding');
        return;
      }

      // Get candidates from the company
      const candidates = await this.candidateModel.findAll({
        where: { companyId: company.id },
        limit: 3, // Only seed compensation for first 3 candidates
      });

      if (candidates.length === 0) {
        console.log('No candidates found, skipping compensation seeding');
        return;
      }

      // Sample compensation data
      const compensationData: Partial<Compensation>[] = [
        {
          candidateId: candidates[0].id,
          effectiveFrom: new Date('2024-09-01'),
          currency: 'USD',
          frequency: 'MONTHLY',
          totalSalary: 75000,
          basicSalary: 60000, // 80% of total as basic
          housing: 10000,
          transportation: 5000,
        },
        {
          candidateId: candidates[1].id,
          effectiveFrom: new Date('2024-09-15'),
          currency: 'USD',
          frequency: 'MONTHLY',
          totalSalary: 95000,
          basicSalary: 76000, // 80% of total as basic
          housing: 12000,
          transportation: 7000,
        },
        {
          candidateId: candidates[2].id,
          effectiveFrom: new Date('2024-10-01'),
          currency: 'USD',
          frequency: 'MONTHLY',
          totalSalary: 120000,
          basicSalary: 96000, // 80% of total as basic
          housing: 15000,
          transportation: 9000,
        },
      ];

      // Create compensation records
      for (const compData of compensationData) {
        await this.compensationModel.findOrCreate({
          where: { candidateId: compData.candidateId },
          defaults: compData,
        });
      }

      console.log('Compensation seeding completed successfully');
    } catch (error) {
      console.error('Error seeding compensation:', error);
      throw new Error(error);
    }
  }
}
