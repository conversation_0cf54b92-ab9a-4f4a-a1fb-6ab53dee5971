'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add status column to Candidates table
    await queryInterface.addColumn('Candidates', 'status', {
      type: Sequelize.ENUM('accepted', 'sent', 'rejected', 'pending', 'success-move-forward', 'scheduled', 'not-success-reject', 'on-hold'),
      allowNull: false,
      defaultValue: 'pending',
    });

    // Migrate existing invitation statuses from InterviewDetails to Candidates
    const [interviewDetails] = await queryInterface.sequelize.query(
      'SELECT id, candidateId, invitationStatus FROM "InterviewDetails" WHERE invitationStatus IS NOT NULL',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Update candidate statuses based on existing interview invitation statuses
    for (const detail of interviewDetails) {
      await queryInterface.sequelize.query(
        'UPDATE "Candidates" SET status = :status WHERE id = :candidateId',
        {
          replacements: {
            status: detail.invitationStatus,
            candidateId: detail.candidateId,
          },
          type: Sequelize.QueryTypes.UPDATE,
        }
      );
    }

    // Remove invitationStatus column from InterviewDetails table
    await queryInterface.removeColumn('InterviewDetails', 'invitationStatus');
  },

  async down(queryInterface, Sequelize) {
    // Add back invitationStatus column to InterviewDetails table
    await queryInterface.addColumn('InterviewDetails', 'invitationStatus', {
      type: Sequelize.ENUM('accepted', 'sent', 'rejected', 'pending', 'success-move-forward', 'scheduled', 'not-success-reject', 'on-hold'),
      allowNull: false,
      defaultValue: 'pending',
    });

    // Migrate statuses back from Candidates to InterviewDetails
    const [candidates] = await queryInterface.sequelize.query(
      'SELECT c.id, c.status, i.id as interviewId FROM "Candidates" c LEFT JOIN "InterviewDetails" i ON c.id = i.candidateId WHERE c.status IS NOT NULL',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Update interview invitation statuses based on candidate statuses
    for (const candidate of candidates) {
      if (candidate.interviewId) {
        await queryInterface.sequelize.query(
          'UPDATE "InterviewDetails" SET invitationStatus = :status WHERE id = :interviewId',
          {
            replacements: {
              status: candidate.status,
              interviewId: candidate.interviewId,
            },
            type: Sequelize.QueryTypes.UPDATE,
          }
        );
      }
    }

    // Remove status column from Candidates table
    await queryInterface.removeColumn('Candidates', 'status');
  },
};
