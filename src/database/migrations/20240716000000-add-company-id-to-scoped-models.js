'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add companyId to Documents table
    await queryInterface.addColumn('Documents', 'company_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Add companyId to Projects table
    await queryInterface.addColumn('Projects', 'company_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Update Candidates table to make companyId a proper foreign key
    // First, add the foreign key constraint to the existing column
    await queryInterface.addConstraint('Candidates', {
      fields: ['companyId'],
      type: 'foreign key',
      name: 'fk_candidates_company_id',
      references: {
        table: 'Companies',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Add indexes for better performance
    await queryInterface.addIndex('Documents', ['company_id'], {
      name: 'idx_documents_company_id',
    });

    await queryInterface.addIndex('Projects', ['company_id'], {
      name: 'idx_projects_company_id',
    });

    await queryInterface.addIndex('Candidates', ['companyId'], {
      name: 'idx_candidates_company_id',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('Documents', 'idx_documents_company_id');
    await queryInterface.removeIndex('Projects', 'idx_projects_company_id');
    await queryInterface.removeIndex('Candidates', 'idx_candidates_company_id');

    // Remove foreign key constraint from Candidates
    await queryInterface.removeConstraint('Candidates', 'fk_candidates_company_id');

    // Remove companyId columns
    await queryInterface.removeColumn('Documents', 'company_id');
    await queryInterface.removeColumn('Projects', 'company_id');
  },
};
