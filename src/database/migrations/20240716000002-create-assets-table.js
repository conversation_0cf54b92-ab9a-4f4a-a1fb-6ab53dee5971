'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Assets table
    await queryInterface.createTable('Assets', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      assetTag: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'asset_tag',
      },
      assetType: {
        type: Sequelize.ENUM(
          'Laptop',
          'Desktop',
          'Keyboard',
          'Mouse',
          'Monitor',
          'Phone',
          'Tablet',
          'Software License',
          'IDE License',
          'Design Tool',
          'External HDD',
          'Printer',
          'Camera',
          'Headphones',
          'Other'
        ),
        allowNull: false,
        field: 'asset_type',
      },
      modelSoftware: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'model_software',
      },
      osVersion: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'os_version',
      },
      licenseSerial: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'license_serial',
      },
      purchaseDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'purchase_date',
      },
      expirationDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'expiration_date',
      },
      status: {
        type: Sequelize.ENUM(
          'Available',
          'In Use',
          'Maintenance',
          'Retired',
          'Lost',
          'Damaged',
          'Active'
        ),
        allowNull: false,
        defaultValue: 'Available',
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'assigned_to',
        references: {
          model: 'Users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'company_id',
        references: {
          model: 'Companies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      purchasePrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        field: 'purchase_price',
      },
      vendor: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      warrantyInfo: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'warranty_info',
      },
      warrantyExpiry: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'warranty_expiry',
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      location: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      serialNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'serial_number',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    });

    // Add unique constraint for assetTag per company
    await queryInterface.addConstraint('Assets', {
      fields: ['asset_tag', 'company_id'],
      type: 'unique',
      name: 'unique_asset_tag_per_company',
    });

    // Add indexes for better performance
    await queryInterface.addIndex('Assets', ['company_id']);
    await queryInterface.addIndex('Assets', ['assigned_to']);
    await queryInterface.addIndex('Assets', ['status']);
    await queryInterface.addIndex('Assets', ['asset_type']);
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('Assets', ['asset_type']);
    await queryInterface.removeIndex('Assets', ['status']);
    await queryInterface.removeIndex('Assets', ['assigned_to']);
    await queryInterface.removeIndex('Assets', ['company_id']);

    // Remove unique constraint
    await queryInterface.removeConstraint('Assets', 'unique_asset_tag_per_company');

    // Drop table
    await queryInterface.dropTable('Assets');
  },
};
