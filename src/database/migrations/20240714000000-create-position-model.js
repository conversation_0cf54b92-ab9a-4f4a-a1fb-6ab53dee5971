'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Position table
    await queryInterface.createTable('Positions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      level: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      minSalary: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      maxSalary: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
      },
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Companies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      department_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Departments',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add position_id column to Candidates table
    await queryInterface.addColumn('Candidates', 'position_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Positions',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    // Remove the old position column from Candidates table
    await queryInterface.removeColumn('Candidates', 'position');
  },

  async down(queryInterface, Sequelize) {
    // Add back the position column to Candidates table
    await queryInterface.addColumn('Candidates', 'position', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    // Remove position_id column from Candidates table
    await queryInterface.removeColumn('Candidates', 'position_id');

    // Drop Position table
    await queryInterface.dropTable('Positions');
  },
};
