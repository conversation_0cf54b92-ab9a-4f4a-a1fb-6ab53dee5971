import { <PERSON><PERSON><PERSON>, OnModuleInit, Logger } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Sequelize } from 'sequelize-typescript';
import { modelsList } from 'src/models';
import { AppConfig } from 'src/config/config.interface';
import { SectionSeeder } from './seeders/section.seeder';
import { RoleSeeder } from './seeders/role.seeder';
import { AdminPermissionSeeder } from './seeders/admin.permission.seeder';
import { CountrySeeder } from './seeders/country-seeder';
import { DepartmentSeeder } from './seeders/department-seeder';
import { AdminUserSeeder } from './seeders/admin-user.seeder';
import { initializeDatabase } from './initialize-database';
import { UsersModule } from 'src/modules/users/users.module';
import { CompanySeeder } from './seeders/company.seeder';
import { CompanyRoleSeeder } from './seeders/company.role.seeder';
import { PlatformUserSeeder } from './seeders/user.seeder';
import { UserPermissionSeeder } from './seeders/user.permission.seeder';

@Module({
  imports: [
    SequelizeModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get<AppConfig['database']>('database');
        const nodeEnv = configService.get<string>('NODE_ENV', 'development');

        const isProduction = nodeEnv === 'production';

        return {
          dialect: 'postgres',
          host: dbConfig.host,
          port: dbConfig.port,
          username: dbConfig.username,
          password: dbConfig.password,
          database: dbConfig.database,
          models: [...modelsList],
          autoLoadModels: true,
          synchronize: false,
          logging: false,
          pool: {
            max: 20,
            min: 0,
            acquire: 30000,
            idle: 10000,
          },
          define: {
            timestamps: true,
            underscored: true,
          },
          retry: {
            max: 5,
            timeout: 60000,
          },
        };
      },
    }),
    SequelizeModule.forFeature([...modelsList]),
    UsersModule,
  ],
  controllers: [],
  exports: [SequelizeModule],
  providers: [
    {
      provide: 'DATABASE_INITIALIZER',
      useFactory: async (sequelize: Sequelize) => {
        try {
          await initializeDatabase(sequelize);
          return true;
        } catch (error) {
          console.error('Database initialization failed:', error);
          return false;
        }
      },
      inject: [Sequelize],
    },
    SectionSeeder,
    RoleSeeder,
    AdminPermissionSeeder,
    UserPermissionSeeder,
    CountrySeeder,
    DepartmentSeeder,
    AdminUserSeeder,
    CompanySeeder,
    CompanyRoleSeeder,
    PlatformUserSeeder,
  ],
})
export class DatabaseModule implements OnModuleInit {
  private readonly logger = new Logger(DatabaseModule.name);

  constructor(private sequelize: Sequelize) {}

  async onModuleInit() {
    try {
      await this.sequelize.authenticate();
      this.logger.log(
        'PostgreSQL database connection established successfully.',
      );

      if (process.env.NODE_ENV !== 'production') {
        // await this.sequelize.sync({ alter: true });
        this.logger.log(
          'Database synchronized with `alter: true` (development only).',
        );
      }
    } catch (error) {
      this.logger.error(
        'Failed to connect to the PostgreSQL database',
        error.stack,
      );
    }
  }
}
