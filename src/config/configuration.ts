import { AppConfig } from './config.interface';

export default (): AppConfig => ({
  port: parseInt(process.env.PORT || '3000', 10),

  frontendAppUrl: {
    url: process.env.FRONTEND_APP_URL || 'http://localhost:3100',
  },

  database: {
    url: process.env.DATABASE_URL ?? null,
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432', 10),
    username: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || 'my_secure_password',
    database: process.env.PGDATABASE || 'hr_master',
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'secret-key',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'refresh-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  },

  email: {
    sendgridApiKey: process.env.SENDGRID_API_KEY,
    defaultFromEmail:
      process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
  },

  admin: {
    email: process.env.DEFAULT_ADMIN_EMAIL ?? '<EMAIL>',
    username: process.env.DEFAULT_USERNAME ?? 'admin',
    password: process.env.DEFAULT_ADMIN_PASSWORD ?? 'Admin@123',
  },

  user: {
    defaultPassword: process.env.DEFAULT_USER_PASSWORD ?? 'Temp1234!',
  },

  google: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    callbackUrl: process.env.GOOGLE_CALLBACK_URL,
  },

  throttler: {
    // Short-term rate limiting (1 second)
    short: {
      ttl: parseInt(process.env.THROTTLE_SHORT_TTL || '1000', 10),
      limit: parseInt(process.env.THROTTLE_SHORT_LIMIT || '3', 10),
    },
    // Medium-term rate limiting (10 seconds)
    medium: {
      ttl: parseInt(process.env.THROTTLE_MEDIUM_TTL || '10000', 10),
      limit: parseInt(process.env.THROTTLE_MEDIUM_LIMIT || '20', 10),
    },
    // Long-term rate limiting (1 minute)
    long: {
      ttl: parseInt(process.env.THROTTLE_LONG_TTL || '60000', 10),
      limit: parseInt(process.env.THROTTLE_LONG_LIMIT || '100', 10),
    },
    // Default rate limiting (1 minute)
    default: {
      ttl: parseInt(process.env.THROTTLE_DEFAULT_TTL || '60000', 10),
      limit: parseInt(process.env.THROTTLE_DEFAULT_LIMIT || '10', 10),
    },
  },
  fileStorage: {
    projectId: process.env.FILE_STORAGE_PROJECT_ID || '0',
    url: process.env.FILE_STORAGE_URL || 'http://localhost:3004',
  },
});
