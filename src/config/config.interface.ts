export interface AppConfig {
  port: number;
  frontendAppUrl: {
    url: string;
  };
  database: {
    url?: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
  };
  jwt: {
    secret: string;
    refreshSecret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };
  email: {
    sendgridApiKey?: string;
    defaultFromEmail: string;
  };
  admin: {
    email: string;
    username: string;
    password: string;
  };
  user: {
    defaultPassword: string;
  };
  google: {
    clientId: string;
    clientSecret: string;
    callbackUrl: string;
  };
  throttler: {
    short: {
      ttl: number;
      limit: number;
    };
    medium: {
      ttl: number;
      limit: number;
    };
    long: {
      ttl: number;
      limit: number;
    };
    default: {
      ttl: number;
      limit: number;
    };
  };
  fileStorage: {
    projectId: string;
    url: string;
  };
}
