import { Injectable } from '@nestjs/common';
import { Response } from 'express';
import { Sequelize } from 'sequelize-typescript';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(
    private readonly sequelize: Sequelize,
    private readonly configService: ConfigService,
  ) {}

  getHello(): string {
    return 'Hello World!';
  }

  async getHealthCheck() {
    const startTime = Date.now();

    // Check database connectivity
    let databaseStatus = 'unknown';
    let databaseResponseTime = 0;
    let databaseError = null;

    try {
      const dbStartTime = Date.now();
      await this.sequelize.authenticate();
      databaseResponseTime = Date.now() - dbStartTime;
      databaseStatus = 'connected';
    } catch (error) {
      databaseStatus = 'disconnected';
      databaseError = error.message;
    }

    const totalResponseTime = Date.now() - startTime;

    // Get memory usage in MB
    const memoryUsage = process.memoryUsage();

    return {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '0.0.1',
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
      },
      database: {
        status: databaseStatus,
        responseTime: databaseResponseTime,
        host: this.configService.get('database.host'),
        port: this.configService.get('database.port'),
        database: this.configService.get('database.database'),
        error: databaseError,
      },
      system: {
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
        responseTime: totalResponseTime,
        cpuUsage: process.cpuUsage(),
      },
      services: {
        database: databaseStatus === 'connected' ? 'healthy' : 'unhealthy',
        api: 'healthy',
      },
      checks: {
        database: databaseStatus === 'connected',
        memory: memoryUsage.heapUsed < memoryUsage.heapTotal * 0.9, // 90% threshold
        uptime: process.uptime() > 0,
      },
    };
  }

  getHomePage(res: Response): void {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Management System API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            font-weight: bold;
        }

        h1 {
            color: #2d3748;
            margin-bottom: 1rem;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .subtitle {
            color: #718096;
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .feature:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .feature-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .feature-desc {
            color: #718096;
            font-size: 0.9rem;
        }

        .status {
            background: #48bb78;
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 2rem 0;
            display: inline-block;
        }

        .links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .link {
            background: #667eea;
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .link:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .link.secondary {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .link.secondary:hover {
            background: #667eea;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }

            h1 {
                font-size: 2rem;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">HR</div>
        <h1>HR Management System</h1>
        
        <div class="status">API is Running</div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">👥</div>
                <div class="feature-title">Employee Management</div>
                <div class="feature-desc">Complete employee lifecycle management</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Analytics</div>
                <div class="feature-desc">Performance tracking and reporting</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">Role-Based Access</div>
                <div class="feature-desc">Secure permission system</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Real-time</div>
                <div class="feature-desc">Live notifications and updates</div>
            </div>
        </div>

        <div class="links">
            <a href="/api/health" class="link secondary">Health Check</a>
        </div>
    </div>
</body>
</html>
    `;

    res.setHeader('Content-Type', 'text/html');
    res.send(htmlContent);
  }
}
