import {
  CompanyInternalScopeEnum,
  PlatformSection,
  PlatformSectionCategoryEnum,
  PlatformSectionScopeEnum,
} from 'src/models/platform-section.model';

export enum PLATFORM_SECTION_ENUM {
  HOME = 'home',
  DASHBOARD = 'dashboard',
  USERS = 'users',
  SUBSCRIPTIONS = 'subscriptions',
  EMPLOYEES = 'employees',
  CANDIDATES = 'candidates',
  TIME_ATTENDANCE = 'time-attendance',
  PAYROLLS = 'payrolls',
  LEAVE_MANAGEMENT = 'leave-management',
  DOCUMENTS = 'documents',
  PERFORMANCE = 'performance',
  PROJECT_BONUSES = 'project-bonuses',
  COMPANY = 'company',
  CHAT = 'chat',
  AI_ASSISTANCE = 'ai-assistance',
  SETTINGS = 'settings',
}

export const PANNEL_SECTIONS_NAMES_LIST: Partial<PlatformSection>[] = [
  {
    name: 'Home',
    order: 0,
    scope: PlatformSectionScopeEnum.BOTH,
    companyInternalScope: CompanyInternalScopeEnum.INDIVIDUAL_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Dashboard',
    order: 1,
    scope: PlatformSectionScopeEnum.BOTH,
    companyInternalScope: CompanyInternalScopeEnum.COMPANY_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Users',
    order: 2,
    scope: PlatformSectionScopeEnum.PLATFORM_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Subscriptions',
    order: 2,
    scope: PlatformSectionScopeEnum.PLATFORM_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Employees',
    order: 3,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.COMPANY_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Candidates',
    order: 4,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.COMPANY_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Time Attendence',
    order: 5,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Payrolls',
    order: 6,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Leave Management',
    order: 7,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Documents',
    order: 8,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Performance',
    order: 9,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Project Bonuses',
    order: 10,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Company',
    order: 11,
    scope: PlatformSectionScopeEnum.COMPANY_ONLY,
    companyInternalScope: CompanyInternalScopeEnum.COMPANY_ONLY,
    category: PlatformSectionCategoryEnum.MAIN_MENU,
  },
  {
    name: 'Chat',
    order: 12,
    scope: PlatformSectionScopeEnum.BOTH,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.OTHER,
  },
  {
    name: 'AI Assistance',
    order: 13,
    scope: PlatformSectionScopeEnum.BOTH,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.OTHER,
  },
  {
    name: 'Settings',
    order: 14,
    scope: PlatformSectionScopeEnum.BOTH,
    companyInternalScope: CompanyInternalScopeEnum.BOTH,
    category: PlatformSectionCategoryEnum.OTHER,
  },
];

export const PUBLIC_ROUTES = [
  '/',
  '/health',
  '/admin/login',
  '/admin/refresh',
  '/auth/signup',
  '/auth/login',
  '/auth/refresh',
];
