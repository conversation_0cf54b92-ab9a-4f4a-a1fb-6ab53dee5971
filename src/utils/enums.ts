export enum GENDER_TYPES_ENUM {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum MARITAL_STATUS_ENUM {
  SINGLE = 'single',
  MARRIED = 'married',
  WIDOWED = 'widowed',
}

export enum USER_ACCOUNT_STATUS_ENUM {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLOCKED = 'blocked',
  PENDING = 'pending',
  VERIFIED = 'verified',
  DELETED = 'deleted',
}

export enum ROLE_SCOPE_ENUM {
  PLATFORM = 'platform',
  COMPANY = 'company',
}

// Permission Action Enums
export enum PERMISSION_ACTION_ENUM {
  VIEW = 'canView',
  CREATE = 'canCreate',
  EDIT = 'canEdit',
  DELETE = 'canDelete',
}
