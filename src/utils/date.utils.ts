/**
 * Returns the current date/time in the specified IANA timezone as a Date object.
 * @param timeZone - IANA timezone string (e.g., 'Asia/Dubai', 'America/New_York')
 */
export function getNowInTimezone(timeZone: string): Date {
  const now = new Date();
  const tzString = now.toLocaleString('en-US', { timeZone });
  return new Date(tzString);
}

const DUBAI_TZ = 'Asia/Dubai';

export function getMondayOfCurrentWeek(
  today: Date = getNowInTimezone(DUBAI_TZ),
): Date {
  const day = today.getDay();
  const diff = today.getDate() - day + (day === 0 ? -6 : 1);
  const monday = new Date(today);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);
  return monday;
}

export function formatDate(date: Date, timeZone: string = DUBAI_TZ): string {
  // Always format as YYYY-MM-DD in the given timezone
  const tzString = date.toLocaleString('en-US', { timeZone });
  const tzDate = new Date(tzString);
  return tzDate.toISOString().slice(0, 10);
}

export function clampToToday(date: Date, timeZone: string = DUBAI_TZ): Date {
  const today = getNowInTimezone(timeZone);
  today.setHours(0, 0, 0, 0);
  return date > today ? today : date;
}

export function isFutureDate(date: Date, timeZone: string = DUBAI_TZ): boolean {
  const today = getNowInTimezone(timeZone);
  today.setHours(0, 0, 0, 0);
  return date > today;
}

export function isWeekend(
  date: Date,
  workingDays: string[],
  timeZone: string = DUBAI_TZ,
): boolean {
  const dayOfWeek = date.toLocaleDateString('en-US', {
    weekday: 'short',
    timeZone,
  });
  return !workingDays.includes(dayOfWeek);
}

export function isHoliday(
  date: Date,
  holidays: string[],
  timeZone: string = DUBAI_TZ,
): boolean {
  const tzString = date.toLocaleString('en-US', { timeZone });
  const tzDate = new Date(tzString);
  return holidays.includes(tzDate.toDateString());
}

export function diffMinutes(start: Date, end: Date): number {
  return Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
}

export function formatDuration(minutes: number): string {
  const h = Math.floor(minutes / 60);
  const m = minutes % 60;
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
}
