import { Column, DataType, HasMany, Model, Table } from 'sequelize-typescript';
import { Permission } from './roles-and-permissions/permission.model';

export enum PlatformSectionScopeEnum {
  PLATFORM_ONLY = 'platform-only',
  COMPANY_ONLY = 'company-only',
  BOTH = 'both',
}

export enum CompanyInternalScopeEnum {
  INDIVIDUAL_ONLY = 'individual-only',
  COMPANY_ONLY = 'company-only',
  BOTH = 'both',
}

export enum PlatformSectionCategoryEnum {
  MAIN_MENU = 'main-menu',
  OTHER = 'other',
}

@Table({})
export class PlatformSection extends Model {
  @Column({ primaryKey: true, autoIncrement: true, type: DataType.INTEGER })
  id: number;

  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: PlatformSectionScopeEnum.BOTH,
  })
  scope: PlatformSectionScopeEnum;

  @Column({
    type: DataType.ENUM(...Object.values(CompanyInternalScopeEnum)),
    defaultValue: CompanyInternalScopeEnum.BOTH,
    allowNull: false,
  })
  companyInternalScope: CompanyInternalScopeEnum;

  @Column({ type: DataType.INTEGER, allowNull: false, defaultValue: 0 })
  order: number;

  @Column({
    type: DataType.ENUM(...Object.values(PlatformSectionCategoryEnum)),
    defaultValue: PlatformSectionCategoryEnum.MAIN_MENU,
    allowNull: false,
  })
  category: PlatformSectionCategoryEnum;

  @HasMany(() => Permission)
  permissions: Permission[];
}
