import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';

@Table({})
export class OnboardingData extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  personalInfo: any;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  emergencyContacts: any;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  policiesAccepted: any;

  @Column({
    type: DataType.JSONB,
  })
  documents: any;

  @Column({
    type: DataType.JSONB,
  })
  qualifications: any;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  startDate: Date;

  @Column({
    type: DataType.STRING,
    defaultValue: 'pending',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 1,
  })
  completedSteps: number;

  @Column({
    type: DataType.DATE,
  })
  completionDate: Date;

  @BelongsTo(() => User)
  user: User;
}
