import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({})
export class Country extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Nationality known as (e.g., American, Chinese, Pakistani)',
  })
  nationality: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'ISO country code (e.g., US, CN, PK)',
  })
  countryCode: string;
}
