import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { DocumentType } from 'src/modules/documents/enums/doucument.type.enum';

@Table({})
export class Document extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.STRING,
    allowNull: false, // Mapping to a potential existing column in the database
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false, // Mapping to a potential existing column in the database
  })
  type: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  number: string;

  @Column({
    type: DataType.STRING,
  })
  frontSideDocumentUrl: string;

  @Column({
    type: DataType.STRING,
  })
  backSideDocumentUrl: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  uploadedBy: number;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  uploadedAt: Date;

  @Column({
    type: DataType.TEXT,
  })
  content: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  issuingDate: Date;

  @Column({
    type: DataType.DATE,
  })
  expiryDate: Date;

  // Relationships
  @BelongsTo(() => User, { foreignKey: 'userId' })
  user: User;

  @BelongsTo(() => User, { foreignKey: 'uploadedBy' })
  uploader: User;
}
