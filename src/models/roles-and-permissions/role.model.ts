import {
  Column,
  Model,
  Table,
  DataType,
  HasMany,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Permission } from './permission.model';
import { Company } from '../../models/company.model';
import { ROLE_SCOPE_ENUM as ROLE_ROLE_SCOPE_ENUM } from '../../utils/enums';

@Table({})
export class Role extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.TEXT,
  })
  description: string;

  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;

  @Column({
    type: DataType.ENUM(...Object.values(ROLE_ROLE_SCOPE_ENUM)),
    allowNull: false,
    defaultValue: ROLE_ROLE_SCOPE_ENUM.COMPANY,
  })
  scope: ROLE_ROLE_SCOPE_ENUM;

  @BelongsTo(() => Company)
  company: Company;

  // Relationships
  @HasMany(() => Permission)
  permissions: Permission[];
}
