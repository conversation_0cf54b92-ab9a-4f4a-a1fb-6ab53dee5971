import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PlatformSection } from '../platform-section.model';
import { Role } from './role.model';
import { PermissionLevelEnum } from 'src/modules/roles-and-permissions/permissions/enums/enum';

@Table({})
export class Permission extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => Role)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  roleId: number;

  @Column({
    type: DataType.ENUM(...Object.values(PermissionLevelEnum)),
  })
  level: PermissionLevelEnum;

  @ForeignKey(() => PlatformSection)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  platformSectionId: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  canView: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  canCreate: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  canEdit: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  canDelete: boolean;

  // BelongsTo associations

  @BelongsTo(() => PlatformSection)
  platformSection: PlatformSection;

  @BelongsTo(() => Role)
  role: Role;
}
