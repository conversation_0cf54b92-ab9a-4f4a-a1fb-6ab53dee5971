import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { Company } from './company.model';

export enum AssetType {
  LAPTOP = 'Laptop',
  DESKTOP = 'Desktop',
  KEYBOARD = 'Keyboard',
  MOUSE = 'Mouse',
  MONITOR = 'Monitor',
  PHONE = 'Phone',
  TABLET = 'Tablet',
  SOFTWARE_LICENSE = 'Software License',
  IDE_LICENSE = 'IDE License',
  DESIGN_TOOL = 'Design Tool',
  EXTERNAL_HDD = 'External HDD',
  PRINTER = 'Printer',
  CAMERA = 'Camera',
  HEADPHONES = 'Headphones',
  OTHER = 'Other',
}

export enum AssetStatus {
  AVAILABLE = 'Available',
  IN_USE = 'In Use',
  MAINTENANCE = 'Maintenance',
  RETIRED = 'Retired',
  LOST = 'Lost',
  DAMAGED = 'Damaged',
  ACTIVE = 'Active',
}

@Table({
  tableName: 'Assets',
  timestamps: true,
})
export class Asset extends Model<Asset> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  assetTag: string;

  @Column({
    type: DataType.ENUM(...Object.values(AssetType)),
    allowNull: false,
  })
  assetType: AssetType;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  modelSoftware: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  osVersion: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  licenseSerial: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  purchaseDate: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expirationDate: Date;

  @Column({
    type: DataType.ENUM(...Object.values(AssetStatus)),
    allowNull: false,
    defaultValue: AssetStatus.AVAILABLE,
  })
  status: AssetStatus;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    field: 'assigned_to',
  })
  assignedTo: number;

  @BelongsTo(() => User, { foreignKey: 'assignedTo' })
  assignedUser: User;

  @ForeignKey(() => Company)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    field: 'company_id',
  })
  companyId: number;

  @BelongsTo(() => Company)
  company: Company;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
  })
  purchasePrice: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  warrantyInfo: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  warrantyExpiry: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  notes: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  location: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  serialNumber: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  createdAt: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  updatedAt: Date;
}
