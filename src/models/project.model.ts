import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { ProjectEmployee } from './project-employee.model';
import { ProjectBonus } from './project-bonus.model';

@Table({})
export class Project extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.TEXT,
  })
  description: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  startDate: Date;

  @Column({
    type: DataType.DATE,
  })
  endDate: Date;

  @Column({
    type: DataType.STRING,
    defaultValue: 'active',
  })
  status: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  managerId: number;

  @Column({
    type: DataType.FLOAT,
    defaultValue: 0,
  })
  completionPercentage: number;

  @Column({
    type: DataType.FLOAT,
    defaultValue: 100,
  })
  targetPercentage: number;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  lastUpdated: Date;

  // Relationships
  @BelongsTo(() => User, { foreignKey: 'managerId' })
  manager: User;

  @HasMany(() => ProjectEmployee)
  projectEmployees: ProjectEmployee[];

  @HasMany(() => ProjectBonus)
  projectBonuses: ProjectBonus[];
}
