import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Project } from './project.model';
import { User } from './users-models/user.model';
import { Payroll } from './payroll.model';

@Table({})
export class ProjectBonus extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => Project)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  projectId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  amount: number;

  @Column({
    type: DataType.STRING,
  })
  reason: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  approvedBy: number;

  @Column({
    type: DataType.DATE,
  })
  approvedDate: Date;

  @Column({
    type: DataType.STRING,
    defaultValue: 'pending',
  })
  status: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isProcessed: boolean;

  @Column({
    type: DataType.DATE,
  })
  processedDate: Date;

  @ForeignKey(() => Payroll)
  @Column({
    type: DataType.INTEGER,
  })
  payrollId: number;

  // Relationships
  @BelongsTo(() => Project)
  project: Project;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => User, { foreignKey: 'approvedBy' })
  approver: User;

  @BelongsTo(() => Payroll)
  payroll: Payroll;
}
