import { Column, Model, Table, DataType } from 'sequelize-typescript';

@Table({})
export class Message extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  senderId: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  senderType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  receiverId: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  content: string;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    allowNull: false,
  })
  timestamp: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isRead: boolean;

  // Note: We're not defining explicit relationships here since the sender/receiver
  // could be different types (employee, hr, system), but you could add more specific relationships
  // if you need them in your application
}
