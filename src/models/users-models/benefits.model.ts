import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Compensation } from './compensation.model'; // assumes you have this model

@Table({})
export class Benefit extends Model<Benefit> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  allowance: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  frequency: string; // e.g., "Monthly", "Annually"

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  amount: number;

  @ForeignKey(() => Compensation)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    field: 'compensation_id',
  })
  compensationId: number;

  @BelongsTo(() => Compensation)
  compensation: Compensation;
}
