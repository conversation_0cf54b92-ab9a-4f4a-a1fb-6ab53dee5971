import {
  Table,
  Model,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
  HasMany,
} from 'sequelize-typescript';
import { Candidate } from './candidate.model';
import { Benefit } from './benefits.model';

@Table({})
export class Compensation extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => Candidate)
  @Column({
    type: DataType.INTEGER,
    allowNull: false, // one-to-one
  })
  candidateId: number;

  @BelongsTo(() => Candidate)
  candidate: Candidate;

  @Column({ type: DataType.DATE, allowNull: false })
  effectiveFrom: Date;

  @Column({ type: DataType.DATE, allowNull: true })
  reviewDate: Date;

  @Column({ type: DataType.STRING, allowNull: false })
  currency: string;

  @Column({ type: DataType.STRING, allowNull: false })
  frequency: string;

  @Column({ type: DataType.FLOAT, allowNull: false })
  totalSalary: number;

  @Column({ type: DataType.FLOAT, allowNull: false })
  basicSalary: number;

  @Column({ type: DataType.FLOAT, allowNull: true })
  housing: number;

  @Column({ type: DataType.FLOAT, allowNull: true })
  transportation: number;

  @Column({ type: DataType.FLOAT, allowNull: true })
  others: number;

  @HasMany(() => Benefit)
  benefits: Benefit[];

  @Column({ type: DataType.DATE })
  createdAt: Date;

  @UpdatedAt
  @Column({ type: DataType.DATE })
  updatedAt: Date;
}
