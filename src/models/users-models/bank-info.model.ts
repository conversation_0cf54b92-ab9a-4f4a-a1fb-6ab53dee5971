import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './user.model';
import { Company } from '../company.model';

@Table({})
export class BankInfo extends Model {
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @BelongsTo(() => Company)
  company: Company;

  @Column({ type: DataType.STRING, allowNull: false })
  holderName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  bankName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  branchName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  accountNumber: string;

  @Column({ type: DataType.STRING })
  iban: string;

  @Column({ type: DataType.STRING })
  swiftCode: string;

  @Column({ type: DataType.STRING })
  bankAddress: string;

  @Column({ type: DataType.STRING })
  currency: string;

  @Column({ type: DataType.STRING })
  routingNumber: string;

  @Column({ type: DataType.STRING })
  branchCode: string;

  @Column({ type: DataType.BOOLEAN })
  isPrimary: boolean;
}
