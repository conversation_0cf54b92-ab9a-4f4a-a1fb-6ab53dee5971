import {
  Column,
  Model,
  Table,
  HasOne,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { DataTypes } from 'sequelize';
import { Compensation } from './compensation.model';
import { InterviewDetails } from './interview-details.model';
import { Department } from '../department.model';
import { Country } from '../address-models/country-model';
import { Position } from '../position.model';
import { Company } from '../company.model';

@Table({})
export class Candidate extends Model<Candidate> {
  @Column({
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataTypes.STRING,
    allowNull: false,
  })
  firstName: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  middleName?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: false,
  })
  lastName: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  phone?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  gender?: string;

  @ForeignKey(() => Position)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_id',
  })
  positionId?: number;

  @BelongsTo(() => Position)
  position?: Position;

  @ForeignKey(() => Department)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'department_id',
  })
  departmentId?: number;

  @BelongsTo(() => Department)
  department: Department;

  @ForeignKey(() => Country)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'country_id',
  })
  countryId?: number;

  @BelongsTo(() => Country)
  country: Country;

  @ForeignKey(() => Company)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'company_id',
  })
  companyId: number;

  @BelongsTo(() => Company)
  company: Company;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  resumeURL?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  passportNumber?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  passportURL?: string;

  @Column({
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  })
  passportExpiryDate?: Date;

  @Column({
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  })
  createdAt: Date;

  @Column({
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  })
  updatedAt: Date;

  @HasOne(() => Compensation)
  compensation: Compensation;

  @HasOne(() => InterviewDetails)
  interview: InterviewDetails;
}
