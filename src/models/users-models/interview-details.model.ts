import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';

import { User } from './user.model';
import { Candidate } from './candidate.model';
import { InvitationStatus } from '../../modules/users/enums/invitation-status.enum';

@Table({})
export class InterviewDetails extends Model {
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @BelongsTo(() => Candidate, 'candidateId')
  candidate: Candidate;

  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  interviewerId: number;

  @BelongsTo(() => User, 'interviewerId')
  interviewer: User;

  @Column({ type: DataType.DATE, allowNull: false })
  interviewDate: Date;

  @Column({ type: DataType.TIME, allowNull: true })
  interviewTime: string;

  @Column({ type: DataType.STRING, allowNull: true })
  interviewType: string;

  @Column({ type: DataType.STRING, allowNull: true })
  interviewMode: string;

  @Column({ type: DataType.STRING, allowNull: true })
  location: string;

  @Column({ type: DataType.STRING, allowNull: true })
  meetingLink: string;

  @Column({
    type: DataType.ENUM(...Object.values(InvitationStatus)),
    allowNull: false,
    defaultValue: InvitationStatus.PENDING,
  })
  invitationStatus: InvitationStatus;

  @Column({ type: DataType.STRING, allowNull: true })
  customMessage: string;
  // resumeURL has been moved to Candidate model
}
