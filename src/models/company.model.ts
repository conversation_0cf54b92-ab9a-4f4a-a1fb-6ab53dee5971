import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ContactDetails } from './users-models/contact-details.model';
import { Address } from './address-models/address-model';
import { INDUSTRY_TYPES_ENUM } from 'src/modules/company/enum/industry.enum';

@Table({})
export class Company extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column(DataType.INTEGER)
  id: number;

  @Column({
    type: DataType.STRING,
  })
  logo: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  portalName: string;

  @Column({
    type: DataType.ENUM(...Object.values(INDUSTRY_TYPES_ENUM)),
  })
  industry: INDUSTRY_TYPES_ENUM;

  @Column({
    type: DataType.STRING,
  })
  website: string;

  @Column({
    type: DataType.STRING,
  })
  email: string;

  @ForeignKey(() => Address)
  @Column(DataType.INTEGER)
  addressId: number;

  @BelongsTo(() => Address)
  address: Address;

  @ForeignKey(() => ContactDetails)
  @Column(DataType.INTEGER)
  contactDetailsId: number;

  @BelongsTo(() => ContactDetails)
  contactDetails: ContactDetails;
}
