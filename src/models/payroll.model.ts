import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { ProjectBonus } from './project-bonus.model';
import { User } from './users-models/user.model';

@Table({})
export class Payroll extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  period: string;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  grossAmount: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  netAmount: number;

  @Column({
    type: DataType.FLOAT,
  })
  allowances: number;

  @Column({
    type: DataType.FLOAT,
  })
  deductions: number;

  @Column({
    type: DataType.FLOAT,
  })
  bonuses: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  paymentDate: Date;

  @Column({
    type: DataType.STRING,
    defaultValue: 'pending',
  })
  status: string;

  @Column({
    type: DataType.STRING,
  })
  notes: string;

  // Relationships
  @BelongsTo(() => User, { foreignKey: 'userId' })
  user: User;

  @HasMany(() => ProjectBonus)
  projectBonuses: ProjectBonus[];
}
