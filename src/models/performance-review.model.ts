import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';

@Table({})
export class PerformanceReview extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  reviewerId: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  reviewDate: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  period: string;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  rating: number;

  @Column({
    type: DataType.TEXT,
  })
  strengths: string;

  @Column({
    type: DataType.TEXT,
  })
  weaknesses: string;

  @Column({
    type: DataType.TEXT,
  })
  goalsSet: string;

  @Column({
    type: DataType.STRING,
    defaultValue: 'draft',
  })
  status: string;

  @Column({
    type: DataType.TEXT,
  })
  comments: string;

  // Relationships
  @BelongsTo(() => User, { foreignKey: 'userId' })
  user: User;

  @BelongsTo(() => User, { foreignKey: 'reviewerId' })
  reviewer: User;
}
