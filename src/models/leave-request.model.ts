import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { LeaveRequestStatuses } from 'src/modules/leave-requests/interface/leave.request.statuses.interface';
import { LeaveRequestTypes } from 'src/modules/leave-requests/interface/leave.request.types.interface';

@Table({})
export class LeaveRequest extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  startDate: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  endDate: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: LeaveRequestTypes;

  @Column({
    type: DataType.STRING,
  })
  reason: string;

  @Column({
    type: DataType.STRING,
    defaultValue: LeaveRequestStatuses.PENDING,
  })
  status: LeaveRequestStatuses;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  approvedBy: number;

  @BelongsTo(() => User, { foreignKey: 'approvedBy' })
  approver: User;

  @BelongsTo(() => User)
  user: User;
}
