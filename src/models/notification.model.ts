import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';

@Table({})
export class Notification extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  message: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isRead: boolean;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  recipientId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  relatedUserId: number;

  @Column({
    type: DataType.INTEGER,
  })
  relatedEntityId: number;

  @Column({
    type: DataType.STRING,
  })
  relatedEntityType: string;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    allowNull: false,
  })
  createdAt: Date;

  // Relationships
  @BelongsTo(() => User, { foreignKey: 'recipientId' })
  recipient: User;

  @BelongsTo(() => User, { foreignKey: 'relatedUserId' })
  relatedUser: User;
}
