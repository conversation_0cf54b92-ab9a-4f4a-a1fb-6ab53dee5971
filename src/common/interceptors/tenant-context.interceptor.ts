import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
  Global,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { TenantContextI } from '../types/tenant-context.types';
import { ROLE_SCOPE_ENUM } from '../../utils/enums';
import { PUBLIC_ROUTES } from 'src/utils/constants';
import { InjectModel } from '@nestjs/sequelize';
import { User } from 'src/models/users-models/user.model';

@Global()
@Injectable()
export class TenantContextInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TenantContextInterceptor.name);
  @InjectModel(User)
  private readonly userModel: typeof User;

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    this.logger.debug(`Processing request: ${request.method} ${request.path}`);

    try {
      // Skip for public routes
      const isPublicRoute = this.isPublicRoute(request.path);
      if (isPublicRoute) {
        this.logger.debug(`Skipping public route: ${request.path}`);
        return next.handle();
      }

      // Extract user from JWT (should be available after auth guard)
      const user = request.user;
      const userFromDb = await this.userModel.findByPk(user.id);

      this.logger.debug(
        `User found in request: ${user ? JSON.stringify(user) : 'null'}`,
      );

      if (!user) {
        this.logger.debug('No user found in request, skipping tenant context');
        return next.handle();
      }

      const tenantContext: TenantContextI = {
        userId: user.id,
        scope: user.scope,
        companyId:
          user.scope === ROLE_SCOPE_ENUM.COMPANY ? userFromDb.companyId : null,
      };

      // Attach tenant context to request
      request.tenantContext = tenantContext;

      this.logger.debug(
        `Tenant context set for user ${user.id}: ${JSON.stringify(tenantContext)}`,
      );
    } catch (error) {
      this.logger.error('Error setting tenant context:', error);
      // Don't fail the request, just continue without tenant context
    }

    return next.handle();
  }

  private isPublicRoute(path: string): boolean {
    return PUBLIC_ROUTES.some((route) => {
      // Use exact matching for most routes
      if (route !== '/') {
        return path === route;
      }
      // Only match root path exactly
      return path === '/';
    });
  }
}
