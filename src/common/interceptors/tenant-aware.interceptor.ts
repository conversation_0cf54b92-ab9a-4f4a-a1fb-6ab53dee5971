import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import {
  TENANT_AWARE_KEY,
  TenantAwareOptions,
} from '../decorators/tenant-aware.decorator';
import { TenantContextI } from '../types/tenant-context.types';
import { ROLE_SCOPE_ENUM } from '../../utils/enums';

@Injectable()
export class TenantAwareInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TenantAwareInterceptor.name);

  constructor(private readonly reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // Check if tenant-aware filtering is enabled for this endpoint
        const tenantAwareOptions =
          this.reflector.getAllAndOverride<TenantAwareOptions>(
            TENANT_AWARE_KEY,
            [context.getHandler(), context.getClass()],
          );

        if (!tenantAwareOptions?.enabled) {
          return data;
        }

        // Get tenant context from request
        const request = context.switchToHttp().getRequest();
        const tenantContext: TenantContextI = request.tenantContext;

        if (!tenantContext) {
          this.logger.warn(
            'Tenant context not found for tenant-aware endpoint',
          );
          return data;
        }

        // Apply tenant filtering to the response data
        return this.applyTenantFilteringToResponse(
          data,
          tenantContext,
          tenantAwareOptions,
        );
      }),
    );
  }

  private applyTenantFilteringToResponse(
    data: any,
    tenantContext: TenantContextI,
    options: TenantAwareOptions,
  ): any {
    // If it's a paginated result, filter the data array
    if (data && data.data && Array.isArray(data.data)) {
      const filteredData = this.filterArrayByTenant(
        data.data,
        tenantContext,
        options,
      );
      return {
        ...data,
        data: filteredData,
        meta: {
          ...data.meta,
          total: filteredData.length,
        },
      };
    }

    // If it's a single object with user relation
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      return this.filterObjectByTenant(data, tenantContext, options);
    }

    // If it's an array, filter it
    if (Array.isArray(data)) {
      return this.filterArrayByTenant(data, tenantContext, options);
    }

    return data;
  }

  private filterArrayByTenant(
    array: any[],
    tenantContext: TenantContextI,
    options: TenantAwareOptions,
  ): any[] {
    if (tenantContext.scope === ROLE_SCOPE_ENUM.PLATFORM) {
      // Platform users can see all data
      return array;
    }

    if (
      tenantContext.scope === ROLE_SCOPE_ENUM.COMPANY &&
      tenantContext.companyId
    ) {
      const userRelation = options.userRelation || 'user';
      const userIncludePath = options.userIncludePath || 'employmentDetails';
      const companyIdField = options.companyIdField || 'companyId';

      return array.filter((item) => {
        const user = item[userRelation];
        if (!user) return false;

        const employmentDetails = user[userIncludePath];
        if (!employmentDetails) return false;

        // Handle both single object and array of employment details
        const details = Array.isArray(employmentDetails)
          ? employmentDetails
          : [employmentDetails];
        return details.some(
          (detail: any) => detail[companyIdField] === tenantContext.companyId,
        );
      });
    }

    // For users without company context, return empty array
    return [];
  }

  private filterObjectByTenant(
    obj: any,
    tenantContext: TenantContextI,
    options: TenantAwareOptions,
  ): any {
    if (tenantContext.scope === ROLE_SCOPE_ENUM.PLATFORM) {
      // Platform users can see all data
      return obj;
    }

    if (
      tenantContext.scope === ROLE_SCOPE_ENUM.COMPANY &&
      tenantContext.companyId
    ) {
      const userRelation = options.userRelation || 'user';
      const userIncludePath = options.userIncludePath || 'employmentDetails';
      const companyIdField = options.companyIdField || 'companyId';

      const user = obj[userRelation];
      if (!user) return null;

      const employmentDetails = user[userIncludePath];
      if (!employmentDetails) return null;

      // Handle both single object and array of employment details
      const details = Array.isArray(employmentDetails)
        ? employmentDetails
        : [employmentDetails];
      const hasAccess = details.some(
        (detail: any) => detail[companyIdField] === tenantContext.companyId,
      );

      return hasAccess ? obj : null;
    }

    // For users without company context, return null
    return null;
  }
}
