import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';

export interface Response<T> {
  success: boolean;
  message: string;
  data?: T;
  meta?: any;
  errors?: any;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  constructor(private readonly reflector: Reflector) {}

  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    const customMessage = this.reflector.get<string>(
      'customMessage',
      context.getHandler(),
    );

    const shouldSkip = this.reflector.get<boolean>(
      'skipResponseWrapper',
      context.getHandler(),
    );

    return next.handle().pipe(
      map((data: any) => {
        if (shouldSkip) return data;

        // Auto-detect if data is a shaped object with message/data
        const isSmartResponse =
          data &&
          typeof data === 'object' &&
          ('message' in data || 'data' in data);

        const response: Response<T> = {
          success: true,
          message:
            customMessage ||
            (isSmartResponse && typeof data.message === 'string'
              ? data.message
              : 'Request successful'),
          data: isSmartResponse ? (data.data ?? null) : data,
        };

        if (isSmartResponse && data.meta) {
          response.meta = data.meta;
        }

        if (isSmartResponse && data.errors) {
          response.errors = data.errors;
        }

        return response;
      }),
    );
  }
}
