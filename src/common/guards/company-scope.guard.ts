// src/common/guards/company-scope.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
// import { COMPANY_SCOPED_KEY } from '../decorators/company-scoped-resource.decorator';

@Injectable()
export class CompanyScopeGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(ctx: ExecutionContext): boolean {
    const request = ctx.switchToHttp().getRequest();
    const companyId = request.headers['x-company-id'];

    if (!companyId) {
      throw new UnauthorizedException('Missing x-company-id header');
    }

    request.companyId = parseInt(companyId, 10);

    return true;
  }
}
