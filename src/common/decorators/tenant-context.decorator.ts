import { createParamDecorator, ExecutionContext, Logger } from '@nestjs/common';
import { TenantContextI } from '../types/tenant-context.types';

const logger = new Logger('TenantContextDecorator');

export const TenantContextDecorator = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): TenantContextI | null => {
    const request = ctx.switchToHttp().getRequest();
    const tenantContext = request.tenantContext || null;

    logger.debug(
      `Extracting tenant context: ${tenantContext ? JSON.stringify(tenantContext) : 'null'}`,
    );

    return tenantContext;
  },
);

// Alias for easier use
export const TenantContext = TenantContextDecorator;
