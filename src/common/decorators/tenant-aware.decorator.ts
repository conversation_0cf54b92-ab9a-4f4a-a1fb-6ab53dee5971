import { SetMetadata } from '@nestjs/common';

export const TENANT_AWARE_KEY = 'tenantAware';

export interface TenantAwareOptions {
  enabled?: boolean;
  userRelation?: string;
}

/**
 * Decorator to mark endpoints as tenant-aware
 * This will automatically filter data based on the user's company scope
 */
export const TenantAware = (options: TenantAwareOptions = {}) => {
  return SetMetadata(TENANT_AWARE_KEY, {
    enabled: true,
    userRelation: 'user',
    ...options,
  });
};

/**
 * Decorator to disable tenant-aware filtering for specific endpoints
 */
export const SkipTenantFilter = () => {
  return SetMetadata(TENANT_AWARE_KEY, { enabled: false });
};
