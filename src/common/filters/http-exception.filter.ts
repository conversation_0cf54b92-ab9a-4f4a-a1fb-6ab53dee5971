import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  InternalServerErrorException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ValidationError,
  DatabaseError,
  UniqueConstraintError,
  ForeignKeyConstraintError,
  TimeoutError,
  ConnectionError,
  ConnectionRefusedError,
  AccessDeniedError,
  HostNotFoundError,
  HostNotReachableError,
  InvalidConnectionError,
  ConnectionTimedOutError,
  InstanceError,
  EmptyResultError,
  ExclusionConstraintError,
  UnknownConstraintError,
} from 'sequelize';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  private sanitizeRequestBody(body: any): any {
    if (!body) return body;

    const sanitized = { ...body };
    const sensitiveFields = [
      'password',
      'token',
      'refreshToken',
      'secret',
      'apiKey',
    ];

    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private getFriendlyErrorMessage(exception: any, status: number): string {
    // Provide user-friendly messages for common errors
    if (status === HttpStatus.CONFLICT) {
      return 'This resource already exists. Please try a different value.';
    }

    if (status === HttpStatus.BAD_REQUEST) {
      if (exception instanceof ValidationError) {
        return 'Please check your input and try again.';
      }
      return 'Invalid request. Please check your input.';
    }

    if (status === HttpStatus.NOT_FOUND) {
      return 'The requested resource was not found.';
    }

    if (status === HttpStatus.REQUEST_TIMEOUT) {
      return 'The request took too long to process. Please try again.';
    }

    if (status === HttpStatus.SERVICE_UNAVAILABLE) {
      return 'Service temporarily unavailable. Please try again later.';
    }

    if (status >= 500) {
      return 'An internal server error occurred. Please try again later.';
    }

    return 'An error occurred. Please try again.';
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Handle different types of errors
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message: string | object = 'Internal server error';
    let errorDetails: any = null;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const response = exception.getResponse();
      message =
        typeof response === 'string'
          ? response
          : response['message'] || response;
      errorDetails = response;
    } else if (exception instanceof ValidationError) {
      // Handle Sequelize validation errors
      status = HttpStatus.BAD_REQUEST;
      message = 'Validation error';
      errorDetails = exception.errors.map((err) => ({
        field: err.path,
        message: err.message,
        value: err.value,
      }));
    } else if (exception instanceof UniqueConstraintError) {
      // Handle unique constraint violations
      status = HttpStatus.CONFLICT;
      message = 'Resource already exists';
      errorDetails = {
        name: exception.name,
        fields: exception.fields,
        message: exception.message,
      };
    } else if (exception instanceof ForeignKeyConstraintError) {
      // Handle foreign key constraint violations
      status = HttpStatus.BAD_REQUEST;
      message = 'Referenced resource not found';
      errorDetails = {
        name: exception.name,
        fields: exception.fields,
        message: exception.message,
      };
    } else if (exception instanceof DatabaseError) {
      // Handle general database errors
      const parentError = exception.parent as any;

      // Check for specific database error types
      if (exception.message.includes('current transaction is aborted')) {
        status = HttpStatus.INTERNAL_SERVER_ERROR;
        message = 'Database transaction error - please try again';
        errorDetails = {
          name: exception.name,
          message: 'Transaction was aborted due to a previous error',
          code: parentError?.code,
        };
      } else if (parentError?.code === '23505') {
        // Unique constraint violation
        status = HttpStatus.CONFLICT;
        message = 'Resource already exists';
        errorDetails = {
          name: exception.name,
          message: exception.message,
          code: parentError?.code,
          constraint: parentError?.constraint,
        };
      } else if (parentError?.code === '23503') {
        // Foreign key constraint violation
        status = HttpStatus.BAD_REQUEST;
        message = 'Referenced resource not found';
        errorDetails = {
          name: exception.name,
          message: exception.message,
          code: parentError?.code,
          constraint: parentError?.constraint,
        };
      } else {
        // General database error
        status = HttpStatus.INTERNAL_SERVER_ERROR;
        message = 'Database error occurred';
        errorDetails = {
          name: exception.name,
          message: exception.message,
          code: parentError?.code,
          detail: parentError?.detail,
          hint: parentError?.hint,
          where: parentError?.where,
          schema: parentError?.schema,
          table: parentError?.table,
          column: parentError?.column,
          dataType: parentError?.dataType,
          constraint: parentError?.constraint,
        };
      }
    } else if (exception instanceof TimeoutError) {
      // Handle timeout errors
      status = HttpStatus.REQUEST_TIMEOUT;
      message = 'Database operation timed out';
      errorDetails = {
        name: exception.name,
        message: exception.message,
      };
    } else if (
      exception instanceof ConnectionError ||
      exception instanceof ConnectionRefusedError ||
      exception instanceof AccessDeniedError ||
      exception instanceof HostNotFoundError ||
      exception instanceof HostNotReachableError ||
      exception instanceof InvalidConnectionError ||
      exception instanceof ConnectionTimedOutError
    ) {
      // Handle connection errors
      status = HttpStatus.SERVICE_UNAVAILABLE;
      message = 'Database connection error';
      errorDetails = {
        name: exception.name,
        message: exception.message,
      };
    } else if (exception instanceof EmptyResultError) {
      // Handle empty result errors
      status = HttpStatus.NOT_FOUND;
      message = 'Resource not found';
      errorDetails = {
        name: exception.name,
        message: exception.message,
      };
    } else if (exception instanceof Error) {
      // Handle other Error instances
      message = exception.message;
      errorDetails = {
        name: exception.name,
        stack:
          process.env.NODE_ENV === 'development' ? exception.stack : undefined,
      };
    }

    // Log the error with appropriate level
    const logData = {
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      error: {
        message,
        details: errorDetails,
      },
      body: this.sanitizeRequestBody(request.body),
      query: request.query,
      params: request.params,
    };

    // Use different log levels based on error type
    if (status >= 500) {
      this.logger.error(
        `[${request.method}] ${request.url} - ${status} - ${message}`,
        logData,
        exception instanceof Error ? exception.stack : undefined,
      );
    } else if (status >= 400) {
      this.logger.warn(
        `[${request.method}] ${request.url} - ${status} - ${message}`,
        logData,
      );
    } else {
      this.logger.log(
        `[${request.method}] ${request.url} - ${status} - ${message}`,
        logData,
      );
    }

    // Send response
    const friendlyMessage = this.getFriendlyErrorMessage(exception, status);
    response.status(status).json({
      success: false,
      message: friendlyMessage,
      data: null,
      errors: errorDetails,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
