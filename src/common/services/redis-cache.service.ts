import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Redis as RedisClient } from 'ioredis';

// Redis SET command modes
type RedisSetMode = 'NX' | 'XX';

@Injectable()
export class RedisCacheService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClient;
  private readonly logger = new Logger(RedisCacheService.name);
  private readonly defaultTtl: number;

  // Redis SET command modes
  private readonly REDIS_SET_MODES = {
    NX: 'NX', // Only set the key if it does not exist
    XX: 'XX', // Only set the key if it already exists
  } as const satisfies Record<string, RedisSetMode>;

  constructor(private readonly configService: ConfigService) {
    this.defaultTtl = this.configService.get<number>('REDIS_DEFAULT_TTL', 600);
  }

  onModuleInit() {
    try {
      const host = this.configService.get<string>('REDIS_HOST', 'localhost');
      const port = this.configService.get<number>('REDIS_PORT', 6379);
      const password = this.configService.get<string>(
        'REDIS_PASSWORD',
        undefined,
      );
      this.client = new Redis({
        host,
        port,
        // TODO: Make redis password optional
        // password: password || undefined,
      });
      this.logger.log(`Connected to Redis at ${host}:${port}`);
      this.client.on('error', (err) => {
        this.logger.error('Redis error:', err);
      });
    } catch (err) {
      this.logger.error('Failed to initialize Redis:', err);
    }
  }

  onModuleDestroy() {
    if (this.client) {
      this.client.quit();
      this.logger.log('Redis connection closed');
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await this.client.get(key);
      if (!data) {
        this.logger.debug(`Cache miss for key: ${key}`);
        return null;
      }
      this.logger.debug(`Cache hit for key: ${key}`);
      return JSON.parse(data) as T;
    } catch (err) {
      this.logger.error(`Error getting key ${key} from Redis:`, err);
      return null;
    }
  }

  async set<T>(
    key: string,
    value: T,
    ttlSeconds?: number,
    mode?: keyof typeof this.REDIS_SET_MODES,
  ): Promise<boolean> {
    try {
      const data = JSON.stringify(value);
      const ttl = ttlSeconds || this.defaultTtl;

      if (mode) {
        // Use the correct overload for mode + EX + ttl
        const result = await this.client.set(
          key,
          data,
          'EX',
          ttl,
          this.REDIS_SET_MODES[mode] as any,
        );
        this.logger.debug(
          `Set cache for key: ${key} (ttl: ${ttl}s, mode: ${mode})`,
        );
        return result === 'OK';
      } else {
        await this.client.set(key, data, 'EX', ttl);
        this.logger.debug(`Set cache for key: ${key} (ttl: ${ttl}s)`);
        return true;
      }
    } catch (err) {
      this.logger.error(`Error setting key ${key} in Redis:`, err);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      const result = await this.client.del(key);
      this.logger.debug(`Deleted cache for key: ${key}`);
      return result > 0;
    } catch (err) {
      this.logger.error(`Error deleting key ${key} from Redis:`, err);
      return false;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    try {
      const keys = await this.client.keys(pattern);
      this.logger.debug(
        `Found ${keys.length} keys matching pattern: ${pattern}`,
      );
      return keys;
    } catch (err) {
      this.logger.error(
        `Error getting keys with pattern ${pattern} from Redis:`,
        err,
      );
      return [];
    }
  }
}
