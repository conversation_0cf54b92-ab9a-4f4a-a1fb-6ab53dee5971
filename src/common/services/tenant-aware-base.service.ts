import { Injectable, Logger } from '@nestjs/common';
import { IncludeOptions, WhereOptions } from 'sequelize';
import { TenantContextI } from '../types/tenant-context.types';
import { ROLE_SCOPE_ENUM } from '../../utils/enums';
import {
  CrudHelperService,
  PaginatedResult,
} from '../crud-helper/crud-helper.service';
import { User } from '../../models/users-models/user.model';

export interface TenantAwareOptions {
  model: any;
  userRelation?: string; // Default: 'user'
  additionalIncludes?: IncludeOptions[];
  additionalWhere?: WhereOptions;
}

@Injectable()
export class TenantAwareBaseService {
  private readonly logger = new Logger(TenantAwareBaseService.name);

  constructor(protected readonly crudHelperService: CrudHelperService) {}

  async findAllWithTenantFiltering(
    model: any,
    tenantContext: TenantContextI,
    options: TenantAwareOptions,
  ): Promise<any[]> {
    const {
      userRelation = 'user',
      additionalIncludes = [],
      additionalWhere = {},
    } = options;

    // For company-scoped users, filter by company
    if (
      tenantContext.scope === ROLE_SCOPE_ENUM.COMPANY &&
      tenantContext.companyId
    ) {
      return this.crudHelperService.findAll(model, {
        include: [
          {
            model: User,
            as: userRelation,
            where: { companyId: tenantContext.companyId },
            ...additionalIncludes,
          },
        ],
        where: additionalWhere,
      });
    }

    // For platform users, return all data
    if (tenantContext.scope === ROLE_SCOPE_ENUM.PLATFORM) {
      return this.crudHelperService.findAll(model, {
        include: [
          {
            model: User,
            as: userRelation,
            ...additionalIncludes,
          },
        ],
        where: additionalWhere,
      });
    }

    return [];
  }

  async findOneWithTenantFiltering(
    model: any,
    id: number,
    tenantContext: TenantContextI,
    options: TenantAwareOptions,
  ): Promise<any> {
    const {
      userRelation = 'user',
      additionalIncludes = [],
      additionalWhere = {},
    } = options;

    // For company-scoped users, filter by company
    if (
      tenantContext.scope === ROLE_SCOPE_ENUM.COMPANY &&
      tenantContext.companyId
    ) {
      return this.crudHelperService.findOne(model, {
        where: { id, ...additionalWhere },
        include: [
          {
            model: User,
            as: userRelation,
            where: { companyId: tenantContext.companyId },
            ...additionalIncludes,
          },
        ],
      });
    }

    // For platform users, return the record
    if (tenantContext.scope === ROLE_SCOPE_ENUM.PLATFORM) {
      return this.crudHelperService.findOne(model, {
        where: { id, ...additionalWhere },
        include: [
          {
            model: User,
            as: userRelation,
            ...additionalIncludes,
          },
        ],
      });
    }

    return null;
  }

  async paginateWithTenantFiltering(
    model: any,
    tenantContext: TenantContextI,
    options: TenantAwareOptions & {
      page: number;
      limit: number;
    },
  ): Promise<PaginatedResult<any>> {
    const {
      userRelation = 'user',
      additionalIncludes = [],
      additionalWhere = {},
      page,
      limit,
    } = options;

    // For company-scoped users, filter by company
    if (
      tenantContext.scope === ROLE_SCOPE_ENUM.COMPANY &&
      tenantContext.companyId
    ) {
      return this.crudHelperService.paginateWithQuery(model, {
        page,
        limit,
        include: [
          {
            model: User,
            as: userRelation,
            where: { companyId: tenantContext.companyId },
            ...additionalIncludes,
          },
        ],
        where: additionalWhere,
      });
    }

    // For platform users, return all paginated data
    if (tenantContext.scope === ROLE_SCOPE_ENUM.PLATFORM) {
      return this.crudHelperService.paginateWithQuery(model, {
        page,
        limit,
        include: [
          {
            model: User,
            as: userRelation,
            ...additionalIncludes,
          },
        ],
        where: additionalWhere,
      });
    }

    return {
      data: [],
      meta: {
        total: 0,
        page,
        limit,
        lastPage: 0,
      },
    };
  }

  async validateTenantAccess(
    tenantContext: TenantContextI,
    id: number,
    options: TenantAwareOptions,
  ): Promise<any> {
    return this.findOneWithTenantFiltering(
      options.model,
      id,
      tenantContext,
      options,
    );
  }
}
