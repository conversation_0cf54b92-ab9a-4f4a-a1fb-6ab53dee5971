import { Injectable, NotFoundException } from '@nestjs/common';
import {
  Model,
  FindOptions,
  UpdateOptions,
  DestroyOptions,
  WhereOptions,
  Transaction,
} from 'sequelize';

type SequelizeModel<T extends Model> = {
  new (...args: any[]): T;
  findAll(options?: FindOptions): Promise<T[]>;
  findOne(options?: FindOptions): Promise<T | null>;
  create(values?: object, options?: { transaction?: Transaction }): Promise<T>;
  update(values: object, options: UpdateOptions): Promise<[number, T[]]>;
  destroy(options?: DestroyOptions): Promise<number>;
  findAndCountAll(options?: FindOptions): Promise<{ count: number; rows: T[] }>;
};

interface PaginationOptions {
  page?: number;
  limit?: number;
  where?: WhereOptions;
  order?: any[];
  include?: any;
  attributes?: string[];
}

export interface PaginatedResult<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    lastPage: number;
    limit: number;
    totalRecords?: number;
  };
}

@Injectable()
export class CrudHelperService {
  async create<T extends Model>(
    model: SequelizeModel<T>,
    data: Partial<T['_creationAttributes']>,
    transaction?: Transaction,
  ): Promise<Partial<T>> {
    const record = await model.create(data, { transaction });
    return record.get({ plain: true });
  }

  async findAll<T extends Model>(
    model: SequelizeModel<T>,
    options?: FindOptions,
    transaction?: Transaction,
  ): Promise<T[]> {
    // If includes are present, don't use raw: true to preserve associations
    const hasIncludes =
      options?.include &&
      (Array.isArray(options.include) ? options.include.length > 0 : true);
    const shouldUseRaw = !hasIncludes;

    return model.findAll({
      ...options,
      raw: shouldUseRaw,
      nest: shouldUseRaw,
      transaction,
    });
  }

  async findOne<T extends Model>(
    model: SequelizeModel<T>,
    options: FindOptions,
    transaction?: Transaction,
  ): Promise<Partial<T>> {
    // If includes are present, don't use raw: true to preserve associations
    const hasIncludes =
      options.include &&
      (Array.isArray(options.include) ? options.include.length > 0 : true);
    const shouldUseRaw = !hasIncludes;

    const record = await model.findOne({
      ...options,
      raw: shouldUseRaw,
      nest: shouldUseRaw,
      transaction,
    });

    return record;
  }

  async update<T extends Model>(
    model: SequelizeModel<T>,
    data: Partial<T['_creationAttributes']>,
    options: UpdateOptions,
    transaction?: Transaction,
  ): Promise<Partial<T>> {
    const [affectedRows] = await model.update(data, {
      ...options,
      transaction,
    });
    if (affectedRows === 0)
      throw new NotFoundException('Update failed: Record not found');

    if (options.where) {
      const updatedRecord = await model.findOne({
        where: options.where,
        raw: true,
        nest: true,
        transaction,
      });

      if (!updatedRecord)
        throw new NotFoundException('Updated record not found');

      return updatedRecord;
    }

    return data as unknown as Partial<T>;
  }

  async delete<T extends Model>(
    model: SequelizeModel<T>,
    options: DestroyOptions,
    transaction?: Transaction,
  ): Promise<void> {
    const deletedCount = await model.destroy({ ...options, transaction });
    if (deletedCount === 0)
      throw new NotFoundException('Delete failed: Record not found');
  }

  async paginateAll<T extends Model>(
    model: SequelizeModel<T>,
    page = 1,
    limit = 10,
    transaction?: Transaction,
  ): Promise<PaginatedResult<Partial<T>>> {
    const offset = (page - 1) * limit;
    const { count, rows } = await model.findAndCountAll({
      limit,
      offset,
      raw: true,
      nest: true,
      transaction,
    });

    return {
      data: rows,
      meta: {
        total: count,
        page,
        lastPage: Math.ceil(count / limit),
        limit,
      },
    };
  }

  async paginateWithQuery<T extends Model>(
    model: SequelizeModel<T>,
    {
      page = 1,
      limit = 10,
      where = {},
      order = [['createdAt', 'DESC']],
      include = [],
      attributes,
    }: PaginationOptions,
    transaction?: Transaction,
  ): Promise<PaginatedResult<Partial<T>>> {
    const offset = (page - 1) * limit;
    const hasIncludes =
      include && (Array.isArray(include) ? include.length > 0 : true);
    const shouldUseRaw = !hasIncludes;

    const { count, rows } = await model.findAndCountAll({
      where,
      limit,
      offset,
      order,
      include,
      attributes,
      raw: shouldUseRaw,
      nest: shouldUseRaw,
      transaction,
    });

    return {
      data: rows,
      meta: {
        total: count,
        page,
        lastPage: Math.ceil(count / limit),
        limit,
        totalRecords: count,
      },
    };
  }
}
