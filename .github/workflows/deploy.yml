name: CI & Deploy with depmd

on:
  push:
    branches:
      - prod
      - dev
      - qa
      - staging

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    permissions:
      packages: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Log in to GHCR
        run: |
          echo "${{ secrets.GHCR_TOKEN }}" | docker login ghcr.io -u softbuilders-software-design --password-stdin

      - uses: ./.github/actions/set-tag
        id: set-tag

      - run: echo "IMAGE_TAG=${{ steps.set-tag.outputs.tag }}" >> $GITHUB_ENV

      - name: Generate version tag
        id: version
        run: |
          VERSION_TAG="${{ github.run_number }}-$(date +%Y%m%d%H%M%S)-${GITHUB_SHA::7}"
          echo "VERSION_TAG=$VERSION_TAG" >> $GITHUB_ENV

      - name: Build and Push Docker Image
        uses: ./.github/actions/build-and-push
        with:
          ENV_VARS: >-
            {
              "NODE_ENV": "${{ secrets.NODE_ENV }}",
              "PORT": "${{ secrets.PORT }}",
              "PGUSER": "${{ secrets.PGUSER }}",
              "PGPASSWORD": "${{ secrets.PGPASSWORD }}",
              "PGHOST": "${{ secrets.PGHOST }}",
              "PGPORT": "${{ secrets.PGPORT }}",
              "PGDATABASE": "${{ secrets.PGDATABASE }}",
              "DEFAULT_ADMIN_EMAIL": "${{ secrets.DEFAULT_ADMIN_EMAIL }}",
              "DEFAULT_USERNAME": "${{ secrets.DEFAULT_USERNAME }}",
              "DEFAULT_ADMIN_PASSWORD": "${{ secrets.DEFAULT_ADMIN_PASSWORD }}",
              "G_TOKEN": "${{ secrets.G_TOKEN }}",
              "GHCR_TOKEN": "${{ secrets.GHCR_TOKEN }}",
              "DEPLOY_USER": "${{ secrets.DEPLOY_USER }}",
              "DEPLOY_HOST": "${{ secrets.DEPLOY_HOST }}"
            }
          IMAGE_BASE: ghcr.io/${{ github.repository }}
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
          VERSION_TAG: ${{ env.VERSION_TAG }}

      # - name: Cleanup .env file
      #   run: rm .env

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - uses: ./.github/actions/setup-ssh
        with:
          key: ${{ secrets.DEPLOY_SSH_PRIVATE_KEY }}
          host: ${{ secrets.DEPLOY_HOST }}
          user: ${{ secrets.DEPLOY_USER }}

      - uses: ./.github/actions/set-deploy-image
        id: set-deploy-image
        with:
          github_repository: ${{ github.repository }}
          github_ref_name: ${{ github.ref_name }}

      - name: Echo deploy context
        run: |
          echo "Deploying to: ${{ github.ref_name }}"
          echo "User: ${{ secrets.DEPLOY_USER }}"
          echo "Host: ${{ secrets.DEPLOY_HOST }}"
          echo "Image: ${{ env.IMAGE }}"
          echo "Versioned Image: ${{ env.VERSIONED_IMAGE }}"
          echo "App Name: ${{ env.APP_NAME }}"

      - name: Deploy to remote server
        uses: ./.github/actions/deploy-to-server
        with:
          ghcr_token: ${{ secrets.GHCR_TOKEN }}
          deploy_user: ${{ secrets.DEPLOY_USER }}
          deploy_host: ${{ secrets.DEPLOY_HOST }}
          github_repository: ${{ github.repository }}
          github_ref_name: ${{ github.ref_name }}
          port: ${{ secrets.PORT }}
          image: ${{ env.VERSIONED_IMAGE }}