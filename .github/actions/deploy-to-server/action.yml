name: Deploy to Remote Server
description: Deploy a Docker image to a remote server via SSH

inputs:
  ghcr_token:
    description: 'GitHub Container Registry token for authentication'
    required: true
  deploy_user:
    description: 'SSH user for deployment'
    required: true
  deploy_host:
    description: 'SSH host for deployment'
    required: true
  github_repository:
    description: 'GitHub repository in the format owner/repo'
    required: true
  github_ref_name:
    description: 'GitHub reference name (branch or tag) to deploy'
    required: true
  port:
    description: 'Port to expose the application on the remote server'
    required: true

runs:
  using: "composite"
  steps:
    - run: |
        APP_NAME=$(basename ${{ inputs.github_repository }})-${{ inputs.github_ref_name }}
        IMAGE_TAG=${{ inputs.github_ref_name == 'prod' && 'latest' || inputs.github_ref_name }}
        IMAGE="ghcr.io/${{ inputs.github_repository }}:$IMAGE_TAG"

        echo "Deploying $IMAGE as $APP_NAME..."

        ssh -o StrictHostKeyChecking=no ${{ inputs.deploy_user }}@${{ inputs.deploy_host }} \
        "echo '${{ inputs.ghcr_token }}' | sudo docker login ghcr.io -u softbuilders-software-design --password-stdin && \
          sudo docker stop $APP_NAME || true && \
          sudo docker rm $APP_NAME || true && \
          sudo docker pull $IMAGE && \
          sudo docker run -d --name $APP_NAME -p ${{ inputs.port }}:${{ inputs.port }} $IMAGE"
      shell: bash
