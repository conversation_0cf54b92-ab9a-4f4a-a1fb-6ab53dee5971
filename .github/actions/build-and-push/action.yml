name: 'Build and Push'
description: 'Builds the project and pushes the image to a container registry'

inputs:
  ENV_VARS:
    required: true
    description: 'JSON string of environment variables'
  IMAGE_BASE:
    required: true
    description: 'Base name of the Docker image'
  IMAGE_TAG:
    required: true
    description: 'Tag for the Docker image'
  VERSION_TAG:
    required: true
    description: 'Version tag for the Docker image'

runs:
  using: "composite"
  steps:
    - run: |
        # Parse JSON string into variables
        echo '${{ inputs.ENV_VARS }}' > env.json
        jq -r 'to_entries | map("\(.key)=\(.value|tostring)") | .[]' env.json > .env
        export $(cat .env | xargs)
        rm env.json

        # Build and push Docker image
        docker build -t ${{ inputs.IMAGE_BASE }}:${{ inputs.IMAGE_TAG }} -t ${{ inputs.IMAGE_BASE }}:${{ inputs.VERSION_TAG }} .
        docker push ${{ inputs.IMAGE_BASE }}:${{ inputs.IMAGE_TAG }}
        docker push ${{ inputs.IMAGE_BASE }}:${{ inputs.VERSION_TAG }}

        # Set GitHub Actions env for downstream steps
        echo "IMAGE=${{ inputs.IMAGE_BASE }}:${{ inputs.IMAGE_TAG }}" >> $GITHUB_ENV
        echo "VERSIONED_IMAGE=${{ inputs.IMAGE_BASE }}:${{ inputs.VERSION_TAG }}" >> $GITHUB_ENV
      shell: bash
