# .github/actions/set-tag/action.yml
name: "Set Image Tag"
description: "Determine the image tag based on the GitHub ref"
outputs:
  tag:
    description: "The determined image tag"
    value: ${{ steps.set.outputs.tag }}
runs:
  using: "composite"
  steps:
    - id: set
      shell: bash
      run: |
        if [[ "${GITHUB_REF_NAME}" == "prod" ]]; then
          echo "tag=latest" >> $GITHUB_OUTPUT
        else
          echo "tag=${GITHUB_REF_NAME}" >> $GITHUB_OUTPUT
        fi