name: 'Setup SSH Key'
description: 'Composite action to write your PEM and configure SSH known_hosts'
inputs:
  key:
    description: 'SSH private key (PEM format)'
    required: true
  host:
    description: 'SSH host to scan'
    required: true
  user:
    description: 'SSH username'
    required: true
runs:
  using: 'composite'
  steps:
    - name: Write SSH key and configure
      run: |
        mkdir -p ~/.ssh
        echo "${{ inputs.key }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H "${{ inputs.host }}" >> ~/.ssh/known_hosts
        printf "Host ${{ inputs.host }}\n  User ${{ inputs.user }}\n  IdentityFile ~/.ssh/id_rsa\n" >> ~/.ssh/config
      shell: bash