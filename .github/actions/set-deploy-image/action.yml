name: "Set Deploy Image Variables"
description: "Set IMAGE_TAG, IMAGE, and APP_NAME based on the GitHub ref"
inputs:
  github_repository:
    description: "The full GitHub repository name"
    required: true
  github_ref_name:
    description: "The Git reference name (e.g., branch name)"
    required: true

runs:
  using: "composite"
  steps:
    - shell: bash
      run: |
        if [[ "${{ inputs.github_ref_name }}" == "prod" ]]; then
          echo "IMAGE_TAG=latest" >> $GITHUB_ENV
        else
          echo "IMAGE_TAG=${{ inputs.github_ref_name }}" >> $GITHUB_ENV
        fi

        echo "IMAGE=ghcr.io/${{ inputs.github_repository }}:${{ env.IMAGE_TAG }}" >> $GITHUB_ENV
        echo "APP_NAME=$(basename ${{ inputs.github_repository }})-${{ inputs.github_ref_name }}" >> $GITHUB_ENV